﻿using System;
using System.Collections.Generic;
using System.Data;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraVerticalGrid;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Improved filter action interface - focused only on business logic
    /// </summary>
    public interface IFilterAction
    {
        string DisplayName { get; }
        string ActionTypeIdentifier { get; }

        // Business logic only - no UI concerns
        FilterActionResult Execute(IFilterActionContext context);
        bool ValidateConfiguration(XFilterClass filter, out string errorMessage);

        // Data persistence
        Dictionary<string, object> SerializeActionData();
        void DeserializeActionData(Dictionary<string, object> data);
    }

    /// <summary>
    /// Separate interface for UI configuration
    /// UI concerns are handled by dedicated UI configurators
    /// </summary>
    public interface IFilterActionUIConfigurator
    {
        string ActionTypeIdentifier { get; }
        FilterUIConfiguration GetUIConfiguration();
        void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor);
        void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor);
    }

    /// <summary>
    /// Declarative UI configuration - no code, just data
    /// </summary>
    public class FilterUIConfiguration
    {
        public bool ShowColumnSelection { get; set; }
        public bool ShowFormatControls { get; set; }
        public List<string> AdditionalControlsToShow { get; set; } = new();
        public List<string> AdditionalControlsToHide { get; set; } = new();
        public Dictionary<string, object> ControlValues { get; set; } = new();
    }

    /// <summary>
    /// Clean interface for form data access - no reflection needed
    /// </summary>
    public interface IFormDataAccessor
    {
        string GetSelectedColumn();
        void SetSelectedColumn(string column);
        T GetControlValue<T>(string controlName);
        void SetControlValue(string controlName, object value);
    }

    /// <summary>
    /// Registry that maps actions to their UI configurators
    /// </summary>
    public static class FilterActionUIRegistry
    {
        private static readonly Dictionary<string, IFilterActionUIConfigurator> _uiConfigurators = new();

        static FilterActionUIRegistry()
        {
            // Register UI configurators
            Register(new FormatCellsUIConfigurator());
            Register(new FormatRowsUIConfigurator());
            Register(new RemoveRowsUIConfigurator());
            Register(new SendToTelegramUIConfigurator());
            Register(new BuyWithAccountUIConfigurator());
        }

        public static void Register(IFilterActionUIConfigurator configurator)
        {
            _uiConfigurators[configurator.ActionTypeIdentifier] = configurator;
        }

        public static IFilterActionUIConfigurator GetUIConfigurator(string actionTypeIdentifier)
        {
            return _uiConfigurators.TryGetValue(actionTypeIdentifier, out var configurator)
                ? configurator
                : null;
        }
    }

    /// <summary>
    /// Example: Clean action implementation - only business logic
    /// </summary>
    public class ImprovedSendToTelegramAction : IFilterAction
    {
        public const string IDENTIFIER = "SEND_TO_TELEGRAM";

        public string DisplayName => "Send to Telegram";
        public string ActionTypeIdentifier => IDENTIFIER;

        public FilterActionResult Execute(IFilterActionContext context)
        {
            try
            {
                if (context.CurrentRow == null || context.FilterRule == null)
                    return FilterActionResult.CreateFailure("Missing row data or filter rule");

                var isFit = context.FilterRule.GetEvaluator().Fit(context.CurrentRow);
                if (isFit)
                {
                    // Pure business logic - no UI concerns
                    return FilterActionResult.CreateSuccess($"Telegram match: {context.FilterRule.Alias}");
                }

                return FilterActionResult.CreateSuccess("No match for Telegram");
            }
            catch (Exception ex)
            {
                return FilterActionResult.CreateFailure($"Failed to process Telegram action: {ex.Message}", ex);
            }
        }

        public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
        {
            errorMessage = null;
            return true;
        }

        public Dictionary<string, object> SerializeActionData()
        {
            return new Dictionary<string, object>();
        }

        public void DeserializeActionData(Dictionary<string, object> data)
        {
            // No additional data for this action
        }
    }

    /// <summary>
    /// Example: Separate UI configurator for Telegram action
    /// </summary>
    public class SendToTelegramUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => ImprovedSendToTelegramAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
                // Pure declarative configuration - no code
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Load any UI-specific data if needed
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Save any UI-specific data if needed
        }
    }

    /// <summary>
    /// Example: Format cells UI configurator
    /// </summary>
    public class FormatCellsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatCellsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = true,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            if (!string.IsNullOrEmpty(filter.FormatColumn))
            {
                formAccessor.SetSelectedColumn(filter.FormatColumn);
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            filter.FormatColumn = formAccessor.GetSelectedColumn();
        }
    }

    /// <summary>
    /// Placeholder configurators for other actions
    /// </summary>
    public class FormatRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatRowsAction.IDENTIFIER;
        public FilterUIConfiguration GetUIConfiguration() => new() { ShowFormatControls = true };
        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
    }

    public class RemoveRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RemoveRowsAction.IDENTIFIER;
        public FilterUIConfiguration GetUIConfiguration() => new() { ShowColumnSelection = false, ShowFormatControls = false };
        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
    }

    public class BuyWithAccountUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => BuyWithAccountAction.IDENTIFIER;
        public FilterUIConfiguration GetUIConfiguration() => new() { ShowColumnSelection = false, ShowFormatControls = false };
        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor) { }
    }

    /// <summary>
    /// Example of how FormXfilters would implement IFormDataAccessor
    /// Much cleaner than the reflection-based approach
    /// </summary>
    public partial class FormXfiltersImproved : IFormDataAccessor
    {
        private IFilterAction _currentAction;
        private IFilterActionUIConfigurator _currentUIConfigurator;

        private void boxAction_SelectedIndexChanged_Improved(object sender, EventArgs e)
        {
            var selectedDisplayName = boxAction.Text;
            _currentAction = _availableActions?.FirstOrDefault(a => a.DisplayName == selectedDisplayName);

            if (_currentAction != null)
            {
                // Get the UI configurator for this action
                _currentUIConfigurator = FilterActionUIRegistry.GetUIConfigurator(_currentAction.ActionTypeIdentifier);

                if (_currentUIConfigurator != null)
                {
                    // Apply UI configuration declaratively
                    var uiConfig = _currentUIConfigurator.GetUIConfiguration();
                    ApplyUIConfiguration(uiConfig);

                    // Load data from filter
                    if (CurrentGridRule != null)
                    {
                        CurrentGridRule.ActionHandler = _currentAction;
                        CurrentGridRule.ActionIdentifier = _currentAction.ActionTypeIdentifier;
                        _currentUIConfigurator.LoadDataFromFilter(CurrentGridRule, this);
                    }
                }
            }
        }

        private void ApplyUIConfiguration(FilterUIConfiguration config)
        {
            // Simple, declarative UI updates - no complex logic
            if (config.ShowColumnSelection)
            {
                boxColumns.Visible = true;
                lblFormatColumn.Visible = true;
            }
            else
            {
                boxColumns.Visible = false;
                lblFormatColumn.Visible = false;
            }

            if (config.ShowFormatControls)
            {
                propertyGridControl1.Visible = true;
                lblFormatStyle.Visible = true;
            }
            else
            {
                propertyGridControl1.Visible = false;
                lblFormatStyle.Visible = false;
            }

            // Apply additional control visibility
            foreach (var controlName in config.AdditionalControlsToShow)
            {
                var control = GetControlByName(controlName);
                if (control != null) control.Visible = true;
            }

            foreach (var controlName in config.AdditionalControlsToHide)
            {
                var control = GetControlByName(controlName);
                if (control != null) control.Visible = false;
            }
        }

        // Clean, simple implementation of IFormDataAccessor
        public string GetSelectedColumn() => boxColumns.Text;
        public void SetSelectedColumn(string column) => boxColumns.Text = column;

        public T GetControlValue<T>(string controlName)
        {
            var control = GetControlByName(controlName);
            // Implementation similar to before but cleaner
            return default(T);
        }

        public void SetControlValue(string controlName, object value)
        {
            var control = GetControlByName(controlName);
            // Implementation similar to before but cleaner
        }

        private void btnAccept_Click_Improved(object sender, EventArgs e)
        {
            // Validate and save using the UI configurator
            if (_currentUIConfigurator != null && CurrentGridRule != null)
            {
                if (!_currentAction.ValidateConfiguration(CurrentGridRule, out string errorMessage))
                {
                    XtraMessageBox.Show($"Configuration error: {errorMessage}");
                    return;
                }

                _currentUIConfigurator.SaveDataToFilter(CurrentGridRule, this);
            }

            // Rest of the save logic...
            DialogResult = DialogResult.OK;
            Close();
        }
    }
}
