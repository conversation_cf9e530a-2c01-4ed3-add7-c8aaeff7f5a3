﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Interface for filter actions using the Strategy Pattern - focused only on business logic
    /// </summary>
    public interface IFilterAction
    {
        /// <summary>
        /// Display name shown in the UI
        /// </summary>
        string DisplayName { get; }

        /// <summary>
        /// Unique identifier for serialization
        /// </summary>
        string ActionTypeIdentifier { get; }

        /// <summary>
        /// Execute the filter action - pure business logic only
        /// </summary>
        FilterActionResult Execute(IFilterActionContext context);

        /// <summary>
        /// Validate the action configuration
        /// </summary>
        bool ValidateConfiguration(XFilterClass filter, out string errorMessage);

        /// <summary>
        /// Serialize action-specific data
        /// </summary>
        Dictionary<string, object> SerializeActionData();

        /// <summary>
        /// Deserialize action-specific data
        /// </summary>
        void DeserializeActionData(Dictionary<string, object> data);
    }

    /// <summary>
    /// Interface for UI configuration - separate from business logic
    /// </summary>
    public interface IFilterActionUIConfigurator
    {
        string ActionTypeIdentifier { get; }
        FilterUIConfiguration GetUIConfiguration();
        void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor);
        void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor);
    }

    /// <summary>
    /// Declarative UI configuration - no code, just data
    /// </summary>
    public class FilterUIConfiguration
    {
        public bool ShowColumnSelection { get; set; }
        public bool ShowFormatControls { get; set; }
        public List<string> AdditionalControlsToShow { get; set; } = new List<string>();
        public List<string> AdditionalControlsToHide { get; set; } = new List<string>();
        public Dictionary<string, object> ControlValues { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// Clean interface for form data access - no reflection needed
    /// </summary>
    public interface IFormDataAccessor
    {
        string GetSelectedColumn();
        void SetSelectedColumn(string column);
        T GetControlValue<T>(string controlName);
        void SetControlValue(string controlName, object value);
    }

    /// <summary>
    /// Context interface for action execution
    /// </summary>
    public interface IFilterActionContext
    {
        XFilterClass FilterRule { get; }
        GridView GridView { get; }
        DataTable SourceDataTable { get; }
        DataRow CurrentRow { get; }
    }

    /// <summary>
    /// Result of filter action execution
    /// </summary>
    public class FilterActionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public object Data { get; set; }

        public static FilterActionResult CreateSuccess(string message = null, object data = null)
        {
            return new FilterActionResult { Success = true, Message = message, Data = data };
        }

        public static FilterActionResult CreateFailure(string message, Exception exception = null)
        {
            return new FilterActionResult { Success = false, Message = message, Exception = exception };
        }
    }

    /// <summary>
    /// Implementation of IFilterActionContext
    /// </summary>
    public class FilterActionContext : IFilterActionContext
    {
        public XFilterClass FilterRule { get; set; }
        public GridView GridView { get; set; }
        public DataTable SourceDataTable { get; set; }
        public DataRow CurrentRow { get; set; }
    }

    /// <summary>
    /// Registry that maps actions to their UI configurators
    /// </summary>
    public static class FilterActionUIRegistry
    {
        private static readonly Dictionary<string, IFilterActionUIConfigurator> _uiConfigurators = new Dictionary<string, IFilterActionUIConfigurator>();

        static FilterActionUIRegistry()
        {
            Initialize();
        }

        private static void Initialize()
        {
            // Register UI configurators
            Register(new FormatCellsUIConfigurator());
            Register(new FormatRowsUIConfigurator());
            Register(new RemoveRowsUIConfigurator());
            Register(new SendToTelegramUIConfigurator());
            Register(new BuyWithAccountUIConfigurator());
            Register(new SendToWebhookUIConfigurator());
        }

        public static void Register(IFilterActionUIConfigurator configurator)
        {
            _uiConfigurators[configurator.ActionTypeIdentifier] = configurator;
        }

        public static IFilterActionUIConfigurator GetUIConfigurator(string actionTypeIdentifier)
        {
            return _uiConfigurators.TryGetValue(actionTypeIdentifier, out var configurator)
                ? configurator
                : null;
        }

        public static IEnumerable<IFilterActionUIConfigurator> GetAllUIConfigurators()
        {
            return _uiConfigurators.Values;
        }
    }

    /// <summary>
    /// Factory for creating filter actions with legacy migration support
    /// </summary>
    public static class FilterActionFactory
    {
        private static readonly Dictionary<string, Func<IFilterAction>> _actionRegistry = new Dictionary<string, Func<IFilterAction>>();
        private static readonly Dictionary<string, string> _legacyActionMigration = new Dictionary<string, string>();
        private static bool _initialized = false;

        static FilterActionFactory()
        {
            Initialize();
        }

        private static void Initialize()
        {
            if (_initialized) return;

            // Register all known action types
            RegisterAction<FormatCellsAction>();
            RegisterAction<FormatRowsAction>();
            RegisterAction<RemoveRowsAction>();
            RegisterAction<SendToTelegramAction>();
            RegisterAction<BuyWithAccountAction>();
            RegisterAction<SendToWebhookAction>();

            // Legacy migration mappings
            _legacyActionMigration["Format cells"] = FormatCellsAction.IDENTIFIER;
            _legacyActionMigration["Format rows"] = FormatRowsAction.IDENTIFIER;
            _legacyActionMigration["Remove rows"] = RemoveRowsAction.IDENTIFIER;
            _legacyActionMigration["Send to Telegram"] = SendToTelegramAction.IDENTIFIER;

            _initialized = true;
        }

        private static void RegisterAction<T>() where T : IFilterAction, new()
        {
            var instance = new T();
            _actionRegistry[instance.ActionTypeIdentifier] = () => new T();
        }

        public static IFilterAction CreateAction(string actionIdentifier)
        {
            Initialize();
            return _actionRegistry.TryGetValue(actionIdentifier, out var factory)
                ? factory()
                : null;
        }

        public static IFilterAction CreateFromLegacyAction(string legacyAction)
        {
            Initialize();

            if (string.IsNullOrEmpty(legacyAction))
                return null;

            // Handle "Buy with [username]" pattern
            if (legacyAction.StartsWith("Buy with "))
            {
                var action = CreateAction(BuyWithAccountAction.IDENTIFIER);
                if (action is BuyWithAccountAction buyAction)
                {
                    buyAction.AccountUsername = legacyAction.Substring("Buy with ".Length);
                }
                return action;
            }

            // Handle standard legacy actions
            if (_legacyActionMigration.TryGetValue(legacyAction, out var identifier))
            {
                return CreateAction(identifier);
            }

            return null;
        }

        public static IEnumerable<IFilterAction> GetAllAvailableActions()
        {
            Initialize();
            return _actionRegistry.Values.Select(factory => factory());
        }

        public static void RegisterCustomAction<T>() where T : IFilterAction, new()
        {
            RegisterAction<T>();
        }
    }

    /// <summary>
    /// UI configurator for Format Cells action
    /// </summary>
    public class FormatCellsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatCellsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = true,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            if (!string.IsNullOrEmpty(filter.FormatColumn))
            {
                formAccessor.SetSelectedColumn(filter.FormatColumn);
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            filter.FormatColumn = formAccessor.GetSelectedColumn();
        }
    }

    /// <summary>
    /// UI configurator for Format Rows action
    /// </summary>
    public class FormatRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row formatting
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row formatting
        }
    }

    /// <summary>
    /// UI configurator for Remove Rows action
    /// </summary>
    public class RemoveRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RemoveRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row removal
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row removal
        }
    }

    /// <summary>
    /// UI configurator for Send to Telegram action
    /// </summary>
    public class SendToTelegramUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToTelegramAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to load for Telegram action
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to save for Telegram action
        }
    }

    /// <summary>
    /// UI configurator for Buy with Account action
    /// </summary>
    public class BuyWithAccountUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => BuyWithAccountAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }
    }

    /// <summary>
    /// UI configurator for Send to Webhook action
    /// </summary>
    public class SendToWebhookUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToWebhookAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
        }
    }
}
