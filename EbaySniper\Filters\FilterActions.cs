﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// Interface for filter actions using the Strategy Pattern
    /// </summary>
    public interface IFilterAction
    {
        /// <summary>
        /// Display name shown in the UI
        /// </summary>
        string DisplayName { get; }

        /// <summary>
        /// Unique identifier for serialization
        /// </summary>
        string ActionTypeIdentifier { get; }

        /// <summary>
        /// Configure UI elements when this action is selected
        /// </summary>
        void ConfigureUI(IFilterActionUIContext uiContext);

        /// <summary>
        /// Load action-specific data from filter into UI
        /// </summary>
        void LoadFromFilter(XFilterClass filter, IFilterActionUIContext uiContext);

        /// <summary>
        /// Save action-specific data from UI into filter
        /// </summary>
        void SaveToFilter(XFilterClass filter, IFilterActionUIContext uiContext);

        /// <summary>
        /// Execute the filter action
        /// </summary>
        FilterActionResult Execute(IFilterActionContext context);

        /// <summary>
        /// Validate the action configuration
        /// </summary>
        bool ValidateConfiguration(XFilterClass filter, out string errorMessage);

        /// <summary>
        /// Serialize action-specific data
        /// </summary>
        Dictionary<string, object> SerializeActionData();

        /// <summary>
        /// Deserialize action-specific data
        /// </summary>
        void DeserializeActionData(Dictionary<string, object> data);
    }

    /// <summary>
    /// Context interface for UI operations
    /// </summary>
    public interface IFilterActionUIContext
    {
        FormXfilters Form { get; }
        void ShowUIElements(params Control[] controls);
        void HideUIElements(params Control[] controls);
        void SetControlValue(Control control, object value);
        T GetControlValue<T>(Control control);
    }

    /// <summary>
    /// Context interface for action execution
    /// </summary>
    public interface IFilterActionContext
    {
        XFilterClass FilterRule { get; }
        GridView GridView { get; }
        DataTable SourceDataTable { get; }
        DataRow CurrentRow { get; }
    }

    /// <summary>
    /// Result of filter action execution
    /// </summary>
    public class FilterActionResult
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public Exception Exception { get; set; }
        public object Data { get; set; }

        public static FilterActionResult CreateSuccess(string message = null, object data = null)
        {
            return new FilterActionResult { Success = true, Message = message, Data = data };
        }

        public static FilterActionResult CreateFailure(string message, Exception exception = null)
        {
            return new FilterActionResult { Success = false, Message = message, Exception = exception };
        }
    }

    /// <summary>
    /// Implementation of IFilterActionContext
    /// </summary>
    public class FilterActionContext : IFilterActionContext
    {
        public XFilterClass FilterRule { get; set; }
        public GridView GridView { get; set; }
        public DataTable SourceDataTable { get; set; }
        public DataRow CurrentRow { get; set; }
    }

    /// <summary>
    /// Base implementation for UI context
    /// </summary>
    public class FilterActionUIContext : IFilterActionUIContext
    {
        public FormXfilters Form { get; private set; }

        public FilterActionUIContext(FormXfilters form)
        {
            Form = form;
        }

        public void ShowUIElements(params Control[] controls)
        {
            foreach (var control in controls)
            {
                if (control != null)
                    control.Visible = true;
            }
        }

        public void HideUIElements(params Control[] controls)
        {
            foreach (var control in controls)
            {
                if (control != null)
                    control.Visible = false;
            }
        }

        public void SetControlValue(Control control, object value)
        {
            if (control == null) return;

            switch (control)
            {
                case TextBox textBox:
                    textBox.Text = value?.ToString() ?? "";
                    break;
                case ComboBox comboBox:
                    comboBox.SelectedItem = value;
                    break;
                case CheckBox checkBox:
                    checkBox.Checked = value is bool b && b;
                    break;
                default:
                    // Handle other control types as needed
                    break;
            }
        }

        public T GetControlValue<T>(Control control)
        {
            if (control == null) return default(T);

            object value = control switch
            {
                TextBox textBox => textBox.Text,
                ComboBox comboBox => comboBox.SelectedItem,
                CheckBox checkBox => checkBox.Checked,
                _ => null
            };

            if (value is T typedValue)
                return typedValue;

            try
            {
                return (T)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }
    }

    /// <summary>
    /// Factory for creating filter actions with legacy migration support
    /// </summary>
    public static class FilterActionFactory
    {
        private static readonly Dictionary<string, Func<IFilterAction>> _actionRegistry = new Dictionary<string, Func<IFilterAction>>();
        private static readonly Dictionary<string, string> _legacyActionMigration = new Dictionary<string, string>();
        private static bool _initialized = false;

        static FilterActionFactory()
        {
            Initialize();
        }

        private static void Initialize()
        {
            if (_initialized) return;

            // Register all known action types
            RegisterAction<FormatCellsAction>();
            RegisterAction<FormatRowsAction>();
            RegisterAction<RemoveRowsAction>();
            RegisterAction<SendToTelegramAction>();
            RegisterAction<BuyWithAccountAction>();
            RegisterAction<SendToWebhookAction>();

            // Legacy migration mappings
            _legacyActionMigration["Format cells"] = FormatCellsAction.IDENTIFIER;
            _legacyActionMigration["Format rows"] = FormatRowsAction.IDENTIFIER;
            _legacyActionMigration["Remove rows"] = RemoveRowsAction.IDENTIFIER;
            _legacyActionMigration["Send to Telegram"] = SendToTelegramAction.IDENTIFIER;

            _initialized = true;
        }

        private static void RegisterAction<T>() where T : IFilterAction, new()
        {
            var instance = new T();
            _actionRegistry[instance.ActionTypeIdentifier] = () => new T();
        }

        public static IFilterAction CreateAction(string actionIdentifier)
        {
            Initialize();
            return _actionRegistry.TryGetValue(actionIdentifier, out var factory)
                ? factory()
                : null;
        }

        public static IFilterAction CreateFromLegacyAction(string legacyAction)
        {
            Initialize();

            if (string.IsNullOrEmpty(legacyAction))
                return null;

            // Handle "Buy with [username]" pattern
            if (legacyAction.StartsWith("Buy with "))
            {
                var action = CreateAction(BuyWithAccountAction.IDENTIFIER);
                if (action is BuyWithAccountAction buyAction)
                {
                    buyAction.AccountUsername = legacyAction.Substring("Buy with ".Length);
                }
                return action;
            }

            // Handle standard legacy actions
            if (_legacyActionMigration.TryGetValue(legacyAction, out var identifier))
            {
                return CreateAction(identifier);
            }

            return null;
        }

        public static IEnumerable<IFilterAction> GetAllAvailableActions()
        {
            Initialize();
            return _actionRegistry.Values.Select(factory => factory());
        }

        public static void RegisterCustomAction<T>() where T : IFilterAction, new()
        {
            RegisterAction<T>();
        }
    }
}
