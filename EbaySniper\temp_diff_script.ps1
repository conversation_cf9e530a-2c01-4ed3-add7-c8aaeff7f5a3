# Script: temp_diff_script.ps1
# Objective: Generate individual diff files for changes between master and llm-integration branches.

# 1. Ensure the top-level 'diff' directory exists in the current workspace root.
#    The -Force parameter creates the directory if it doesn't exist,
#    and does nothing if it already exists.
New-Item -ItemType Directory -Path "diff" -Force -ErrorAction SilentlyContinue | Out-Null

# 2. Get the list of files changed between 'master' and 'llm-integration' branches.
#    For each changed file, generate a diff and save it.
#    The diffs will be stored in the 'diff' directory, preserving the original file's path structure.
$changedFiles = git diff --name-only 93e201ead1c873c70da461222a8981e2d57ba2b3..ccf39d16d8f5be616a95c8b6d045c4ef7af29425
if ($null -eq $changedFiles -or $changedFiles.Length -eq 0) {
    Write-Host "No files changed between 93e201ead1c873c70da461222a8981e2d57ba2b3 and ccf39d16d8f5be616a95c8b6d045c4ef7af29425. No diffs generated."
    exit 0
}

$changedFiles | ForEach-Object {
  # Current file path from the list of changed files
  $originalFilePath = $_
  
  # Construct the full path for the .diff file.
  # Example: if $originalFilePath is "src/components/button.js",
  # $outputDiffFile will be "diff/src/components/button.js.diff".
  $outputDiffFile = Join-Path -Path "diff" -ChildPath ($originalFilePath + ".diff")
  
  # Get the directory part of the output .diff file path.
  # Example: "diff\src\components"
  $outputTargetDirectory = Split-Path -Path $outputDiffFile -Parent
  
  # Create the target subdirectory within 'diff\' if it doesn't already exist.
  if ($null -ne $outputTargetDirectory -and $outputTargetDirectory -ne (Resolve-Path -Path "diff").Path -and (-not (Test-Path -Path $outputTargetDirectory -PathType Container))) {
    New-Item -ItemType Directory -Path $outputTargetDirectory -Force -ErrorAction SilentlyContinue | Out-Null
  }
  
  Write-Host "Generating diff for '$originalFilePath' --> '$outputDiffFile'"
  
  # Generate the diff for the specific file against the 'master' branch.
  # --no-prefix makes paths in the diff output cleaner (relative to repo root).
  # Out-File saves the diff content to the constructed path with UTF-8 encoding.
  git diff 93e201ead1c873c70da461222a8981e2d57ba2b3..ccf39d16d8f5be616a95c8b6d045c4ef7af29425 --no-prefix -- $originalFilePath | Out-File -FilePath $outputDiffFile -Encoding utf8
}

Write-Host "Diff generation process complete. Individual diff files are located in the 'diff' directory."