diff --git a/.gitignore b/.gitignore
index 75bde6d8..9b6b2df8 100644
--- a/.gitignore
+++ b/.gitignore
@@ -277,3 +277,23 @@ NDepend*
 /EbaySniper/bin/Debug/CefSharp.WinForms.xml
 /EbaySniper/bin/Debug/CefSharp.xml
 /EbaySniper/bin/Debug/debug.log
+/EbaySniper/bin/Debug/Humanizer.dll
+/EbaySniper/bin/Debug/Humanizer.xml
+/EbaySniper/bin/Debug/Json.More.dll
+/EbaySniper/bin/Debug/Json.More.xml
+/EbaySniper/bin/Debug/JsonPointer.Net.dll
+/EbaySniper/bin/Debug/JsonPointer.Net.xml
+/EbaySniper/bin/Debug/JsonSchema.Net.dll
+/EbaySniper/bin/Debug/JsonSchema.Net.Generation.dll
+/EbaySniper/bin/Debug/JsonSchema.Net.Generation.xml
+/EbaySniper/bin/Debug/JsonSchema.Net.xml
+/EbaySniper/bin/Debug/Mscc.GenerativeAI.dll
+/EbaySniper/bin/Debug/Mscc.GenerativeAI.xml
+/EbaySniper/bin/Debug/System.IO.Pipelines.dll
+/EbaySniper/bin/Debug/System.IO.Pipelines.xml
+/EbaySniper/bin/Debug/System.Text.Encodings.Web.xml
+/EbaySniper/bin/Debug/System.Text.Json.xml
+/memory-bank
+/EbaySniper/bin/Debug/System.IO.Compression.FileSystem.dll
+/EbaySniper/bin/Debug/System.ValueTuple.xml
+*.clinerules
diff --git a/EbaySniper/AI/AiAnalysis.cs b/EbaySniper/AI/AiAnalysis.cs
new file mode 100644
index ********..1ed95bec
--- /dev/null
+++ b/EbaySniper/AI/AiAnalysis.cs
@@ -0,0 +1,132 @@
+﻿using System;
+using System.Collections.Generic;
+using System.ComponentModel;
+using System.Data;
+using System.Diagnostics;
+using System.Threading;
+using System.Threading.Tasks;
+using System.Windows.Forms;
+using DevExpress.Utils.Menu;
+using DevExpress.XtraEditors;
+using DevExpress.XtraGrid.Views.Grid;
+using Newtonsoft.Json;
+using Newtonsoft.Json.Linq;
+using uBuyFirst.CustomClasses;
+using uBuyFirst.Data;
+
+namespace uBuyFirst.AI
+{
+
+    internal class AiAnalysis
+    {
+        public static CustomBindingList<string> AiColumnsList;
+        public static async void UpdateAnalysis(object sender, EventArgs e)
+        {
+            try
+            {
+                var menuItem = sender as DXMenuItem;
+                if (menuItem?.Tag is GridView gridView)
+                {
+                    if (gridView.GridControl.DataSource is DataTable dataTable)
+                    {
+                        // Create an instance of GeminiService
+                        var geminiService = new GeminiService();
+                        Debug.WriteLine("Starting Gemini analysis of product titles...");
+
+                        var semaphore = new SemaphoreSlim(5); // limit concurrency to 5
+                        var tasks = new List<Task>();
+                        var uiContext = SynchronizationContext.Current;
+                        foreach (DataRow row in dataTable.Rows)
+                        {
+                            // Wait until there is room in the semaphore.
+                            await semaphore.WaitAsync();
+
+                            // Launch a task to process this row.
+                            tasks.Add(Task.Run(async () =>
+                            {
+                                try
+                                {
+                                    if (!row.RowState.HasFlag(DataRowState.Deleted) && !row.IsNull("Title"))
+                                    {
+                                        var d = (DataList)row["Blob"];
+
+                                        var title = d.Title;
+                                        var conditionDescription = d.ConditionDescription;
+                                        var description = d.Description;
+                                        var pictures = new List<string> { d.GalleryUrl };
+                                        pictures.AddRange(d.Pictures);
+
+                                        if (!string.IsNullOrEmpty(title))
+                                        {
+                                            Debug.WriteLine($"Processing Title: {title}");
+
+                                            // Send title to Gemini for analysis
+                                            var jsonResponse = await geminiService.AnalyzeTestStripListing(title,
+                                                conditionDescription, description, pictures);
+                                            jsonResponse = jsonResponse.Trim('`').Replace("json\n", "");
+                                            dynamic json = JsonConvert.DeserializeObject(jsonResponse);
+
+                                            // Convert dynamic json to a JObject for iteration.
+                                            var jObject = json as JObject;
+                                            var aiColumns = new List<KeyValuePair<string, string>>();
+
+                                            if (jObject != null)
+                                            {
+                                                foreach (var property in jObject.Properties())
+                                                {
+                                                    aiColumns.Add(new KeyValuePair<string, string>(property.Name,
+                                                        property?.Value?.ToString() ?? ""));
+                                                }
+                                            }
+
+                                            uiContext.Post(_ =>
+                                            {
+                                                // This code runs on the UI thread.
+                                                foreach (var responseKeyValue in aiColumns)
+                                                {
+                                                    if (row.Table.Columns.Contains("ai " + responseKeyValue.Key))
+                                                    {
+                                                        row["ai " + responseKeyValue.Key] = responseKeyValue.Value;
+                                                    }
+                                                }
+                                            }, null);
+
+                                            // Output the analysis to debugger
+                                            Debug.WriteLine($"Gemini Analysis: {jsonResponse}");
+                                            Debug.WriteLine("----------------------------------------");
+                                        }
+                                    }
+                                }
+                                finally
+                                {
+                                    // Always release the semaphore even if an exception occurs.
+                                    semaphore.Release();
+                                }
+                            }));
+                        }
+
+                        // Wait for all tasks to complete.
+                        await Task.WhenAll(tasks);
+                        Debug.WriteLine($"Processed {dataTable.Rows.Count} items with Gemini");
+                    }
+                }
+            }
+            catch (Exception ex)
+            {
+                Debug.WriteLine($"Error in UpdateAnalysis: {ex.Message}");
+                XtraMessageBox.Show($"Error processing grid data: {ex.Message}", "Error",
+                    MessageBoxButtons.OK, MessageBoxIcon.Error);
+            }
+        }
+
+        public static void AiColumnsList_ListChanged(object sender, ListChangedEventArgs e)
+        {
+
+        }
+
+        public static void AiColumnsList_ItemDeleting(object sender, string e)
+        {
+
+        }
+    }
+}
diff --git a/EbaySniper/AI/GeminiService.cs b/EbaySniper/AI/GeminiService.cs
new file mode 100644
index ********..c82c694f
--- /dev/null
+++ b/EbaySniper/AI/GeminiService.cs
@@ -0,0 +1,109 @@
+﻿using System;
+using System.Collections.Generic;
+using System.IO;
+using System.Threading.Tasks;
+using Mscc.GenerativeAI;
+using uBuyFirst.Images;
+
+namespace uBuyFirst.AI
+{
+    public class GeminiService
+    {
+        private readonly GenerativeModel _model;
+        private const string DefaultApiKey = "AIzaSyAXCrD1EsXzuU0Ssnj_ID3_SkV5kZoTs_4";
+
+        public GeminiService() : this(DefaultApiKey)
+        {
+        }
+
+        public GeminiService(string apiKey)
+        {
+            // Initialize Google AI with API key
+            var googleAI = new GoogleAI(apiKey: apiKey);
+
+            // Create a generative model instance
+            _model = googleAI.GenerativeModel(model: Model.Gemini20FlashLite);
+        }
+
+        public async Task<string> SendHelloMessage()
+        {
+            try
+            {
+                // Generate content using the model
+                var response = await _model.GenerateContent("hello");
+
+                // Extract and return the text response
+                return response.Text;
+            }
+            catch (Exception ex)
+            {
+                return $"Error calling Gemini API: {ex.Message}";
+            }
+        }
+
+        public async Task<string> AnalyzeMultipleImages(string prompt, List<string> imageUrls)
+        {
+            try
+            {
+                // Create a request with the prompt
+                var request = new GenerateContentRequest(prompt);
+
+                // Add all images to the request
+                foreach (var imageUrl in imageUrls)
+                {
+                    var imagePath = await ImageTools.GetImageFromDiskOrInternet(imageUrl);
+                    var base64Image = Convert.ToBase64String(File.ReadAllBytes(imagePath));
+                    await request.AddMedia(base64Image, "image/jpeg");
+                }
+
+                // Generate content using the model
+                var response = await _model.GenerateContent(request);
+
+                // Return analysis
+                return response.Text;
+            }
+            catch (Exception ex)
+            {
+                return $"Error analyzing images with Gemini API: {ex.Message}";
+            }
+        }
+
+        public async Task<string> AnalyzeTestStripListing(string title, string conditionDescription, string description, List<string> pictures)
+        {
+            try
+            {
+                // Read system prompt from file
+                string systemPrompt = System.IO.File.ReadAllText("SystemPrompt.txt");
+
+                // Create prompt combining system prompt and listing data
+                string prompt = $"{systemPrompt}\n\nAnalyze this listing:\nTitle: {title}\nCondition: {conditionDescription}\nDescription: {description}";
+
+                // Use AnalyzeMultipleImages to process both text and images
+                return await AnalyzeMultipleImages(prompt, pictures);
+            }
+            catch (Exception ex)
+            {
+                return $"Error analyzing test strip listing with Gemini API: {ex.Message}";
+            }
+        }
+
+        public async Task<string> AnalyzeProductTitle(string title)
+        {
+            try
+            {
+                // Create prompt for title analysis
+                string prompt = $"Analyze this product title and provide key information about the item: {title}";
+
+                // Generate content using the model
+                var response = await _model.GenerateContent(prompt);
+
+                // Return analysis
+                return response.Text;
+            }
+            catch (Exception ex)
+            {
+                return $"Error analyzing title with Gemini API: {ex.Message}";
+            }
+        }
+    }
+}
diff --git a/EbaySniper/BrowseAPI/BrowseAPIParser.cs b/EbaySniper/BrowseAPI/BrowseAPIParser.cs
index 39c2640f..24b7c272 100644
--- a/EbaySniper/BrowseAPI/BrowseAPIParser.cs
+++ b/EbaySniper/BrowseAPI/BrowseAPIParser.cs
@@ -253,6 +253,7 @@ namespace uBuyFirst.BrowseAPI
                 {
                     dataList.SellerName = browseItem.Seller.Username;
                     dataList.StoreName = dataList.SellerName;
+                    //dataList.SellerStore = "Business";
                 }
 
                 if (Enum.TryParse(browseItem.ListingMarketplaceId, out SiteCodeType parsedSite))
diff --git a/EbaySniper/Filters/XFilterClassChild.cs b/EbaySniper/Filters/XFilterClassChild.cs
index 678c7771..cccaf651 100644
--- a/EbaySniper/Filters/XFilterClassChild.cs
+++ b/EbaySniper/Filters/XFilterClassChild.cs
@@ -11,6 +11,7 @@ using DevExpress.Data.Filtering.Exceptions;
 using DevExpress.Data.Filtering.Helpers;
 using DevExpress.XtraEditors;
 using DevExpress.XtraGrid;
+using uBuyFirst.AI;
 using uBuyFirst.Grid;
 using uBuyFirst.Item;
 
@@ -78,10 +79,18 @@ namespace uBuyFirst
             {
                 if (dataTable.Columns.Contains(specific.CategoryName))
                     continue;
-                
+
                 dataTable.Columns.Add(specific.CategoryName);
             }
 
+            foreach (var aiColumn in AiAnalysis.AiColumnsList)
+            {
+                if (dataTable.Columns.Contains(aiColumn))
+                    continue;
+
+                dataTable.Columns.Add(aiColumn);
+            }
+
             try
             {
                 var pds = new PropertyDescriptor[dataTable.Columns.Count];
diff --git a/EbaySniper/Form1.cs b/EbaySniper/Form1.cs
index 4be98f98..47ed824e 100644
--- a/EbaySniper/Form1.cs
+++ b/EbaySniper/Form1.cs
@@ -1,2196 +1,2212 @@
-﻿using System;
-using System.Collections.Generic;
-using System.ComponentModel;
-using System.Diagnostics;
-using System.Drawing;
-using System.Globalization;
-using System.IO;
-using System.IO.Compression;
-using System.Linq;
-using System.Media;
-using System.Net;
-using System.Reflection;
-using System.Text.RegularExpressions;
-using System.Threading;
-using System.Threading.Tasks;
-using System.Windows.Forms;
-using DevExpress.LookAndFeel;
-using DevExpress.Skins;
-using DevExpress.Utils;
-using DevExpress.XtraBars;
-using DevExpress.XtraBars.Docking;
-using DevExpress.XtraBars.Docking2010;
-using DevExpress.XtraBars.Ribbon;
-using DevExpress.XtraBars.Ribbon.Gallery;
-using DevExpress.XtraEditors;
-using DevExpress.XtraEditors.Controls;
-using DevExpress.XtraGrid.Views.BandedGrid;
-using DevExpress.XtraGrid.Views.Base;
-using DevExpress.XtraGrid.Views.Grid;
-using DevExpress.XtraGrid.Views.Grid.ViewInfo;
-using DevExpress.XtraLayout;
-using DevExpress.XtraTreeList;
-using DevExpress.XtraTreeList.Nodes;
-using eBay.Service.Core.Sdk;
-using eBay.Service.Core.Soap;
-using ExceptionReporting;
-using NLog;
-using uBuyFirst.Auth;
-using uBuyFirst.BrowseAPI;
-using uBuyFirst.CefBrowser;
-using uBuyFirst.CustomClasses;
-using uBuyFirst.Data;
-using uBuyFirst.Filters;
-using uBuyFirst.Grid;
-using uBuyFirst.GUI;
-using uBuyFirst.Images;
-using uBuyFirst.Item;
-using uBuyFirst.License;
-using uBuyFirst.MQTT;
-using uBuyFirst.Network;
-using uBuyFirst.Other;
-using uBuyFirst.Prefs;
-using uBuyFirst.Properties;
-using uBuyFirst.Purchasing;
-using uBuyFirst.Purchasing.Cookies;
-using uBuyFirst.Search;
-using uBuyFirst.Search.Status;
-using uBuyFirst.Security;
-using uBuyFirst.Stats;
-using uBuyFirst.SubSearch;
-using uBuyFirst.Telegram;
-using uBuyFirst.Time;
-using uBuyFirst.Tools;
-using uBuyFirst.Update;
-using uBuyFirst.Views;
-using SiteCodeType = eBay.Service.Core.Soap.SiteCodeType;
-
-[assembly:
-    Obfuscation(Exclude = false,
-        Feature = "preset(minimum);"
-                  + "+anti debug;"
-                  + "+anti dump;"
-                  + "+anti ildasm;"
-                  + "+anti tamper(key=dynamic);"
-                  + "+constants;"
-                  + "+ctrl flow;"
-                  + "+invalid metadata;"
-                  + "+ref proxy;"
-                  + "-rename;")]
-[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]
-
-namespace uBuyFirst
-{
-    public partial class Form1
-    {
-        //Modules - Alert, Tracking, Logging, eBay Searches, eBay Accounts, PlaceOffer, Filtering, Pushbullet, Specifics, Browser, Licensing, Settings, Updater
-
-        //tracking
-
-        //lists
-        public static BindingList<EbayAccount> EBayAccountsList;
-        internal QueryList _ebaySearches;
-
-        //Utils
-        private Updater _updater;
-        private readonly GuiChanger _guiChanger;
-        private FocusRouter _focusRouter;
-        private PushbulletSender _pushbullet;
-        private TelegramSender _telegramSender;
-        private SoundPlayer _myPlayer;
-        private readonly AblyClient _ablyClient;
-        private readonly MQTTManager _mqttManager;
-        public static readonly Logger Log = LogManager.GetLogger("Logger");
-        public static LicenseUtility LicenseUtility;
-        public static SecurePhpConnection Secure = new SecurePhpConnection();
-        public FilterAdapter FilterAdapter1;
-
-        public SearchService _searchService;
-
-        //time
-        private System.Windows.Forms.Timer _1SecTimer;
-        private System.Windows.Forms.Timer _1HourTimer;
-        private System.Windows.Forms.Timer _searchTermSyncTimer;
-
-        //Cancellation
-        internal CancellationToken CancelTokenOnRowChange;
-        internal CancellationTokenSource CancelTokenSourceOnRowChange = new CancellationTokenSource();
-
-        //Forms
-        public static Form1 Instance { get; private set; }
-
-        private FormReportDialog _formReportDialog;
-
-        public static SynchronizationContext _synchronizationContext;
-        private static Helpers.AdvancedSearchConfig _requestsCfg;
-        private Point _filterItemLocationPoint = Point.Empty;
-
-        public static bool _showHighBidder;
-        private static GetItemsStatus _checkItemsStatus;
-
-        private static SemaphoreSlim _getItemQueueLimiter;
-
-        private TrayManager _trayManager;
-        private ViewReporter _viewReporter;
-        private PicManager _picManager;
-
-        public static bool EBayAccountInitCompleted { get; set; }
-        private static bool _saveActiveWorkspace = true;
-        public static BuyingService BuyingService;
-        public static Analytics? GoogleAnalytics;
-        private readonly CefBrowserManager _cefBrowserManager;
-
-        private object _chkLargeImagesOnHoverValueChangedLock = new object();
-        public static int LastSliderValueSmall = 100;
-        public static int LastSliderValueLarge = 100;
-
-        private void InitVars()
-        {
-            BaseGallery.AllowHoverAnimation = false;
-            _synchronizationContext = SynchronizationContext.Current;
-            ExM.ExceptionsList = new List<string>();
-            EBayAccountsList = new BindingList<EbayAccount>();
-            EBayAccountsList.ListChanged += EbayAccount.EBayAccountsList_ListChanged;
-            ProgramState.Isdebug = Debugger.IsAttached;
-            ProgramState.Idlesw = new Stopwatch();
-            ProgramState.LastSelectedItemID = "";
-            ProgramState.TotalRunningStopwatch = new Stopwatch();
-            ProgramState.SearchingStopwatchGA4 = new Stopwatch();
-            ExM.Reporter = new ExceptionReporter();
-            ExM.SynchronizationContextCurrent = SynchronizationContext.Current;
-            Stat.TotalItemsProcessed = 0;
-            _myPlayer = new SoundPlayer { Stream = Resources.binbloop };
-
-            ProgramState.PublicIp = "************";
-
-            _focusRouter = new FocusRouter(webBrowser1);
-
-            ResultsView.DockManager = dockManager1;
-            ResultsView.RepositoryItemViews = repositoryItemViews;
-            FilterAdapter1 = new FilterAdapter();
-            _trayManager = new TrayManager(this);
-
-            TopRowFocus.FocusTimer.SynchronizingObject = Instance;
-            TopRowFocus.FocusTimer.AutoReset = false;
-            TopRowFocus.FocusTimer.Elapsed += FocusTimer_Elapsed;
-            TopRowFocus.FocusTimer.SynchronizingObject = this;
-
-            _picManager = new PicManager(galleryControl1);
-
-            //CategoryService = new CategoryService(ap);
-        }
-
-        public Form1()
-        {
-            InitializeComponent();
-            try
-            {
-                ProgramState.UBFVersion = ProductVersion;
-                Instance = this;
-                webBrowser1.PreviewKeyDown += FocusRouter.WebBrowserKeyDown;
-
-                MakeRunButtonStart();
-                dockManager1.BeginUpdate();
-                InitVars();
-                NetTools.InitHttPprotocol();
-
-                //defer
-                Loggers.SetupCloudLogging();
-                Upgrader.TryUpgrade();
-
-                ProgramState.HWID = LicenseUtility.GetHWID();
-                _ablyClient = new AblyClient(_ => { });
-                _mqttManager = new MQTTManager(PostMQQTItemToResultsGrid);
-                Log.Info("Started version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ";" + Environment.OSVersion);
-
-                //if (!ProgramState.Isdebug)
-                {
-                    Program.DeregisterExceptionHandlers();
-                    AppDomain.CurrentDomain.UnhandledException += ExM.ubuyExceptionHandler;
-                    Application.ThreadException += ExM.ubuyExceptionHandler;
-                    TaskScheduler.UnobservedTaskException += ExM.ubuyExceptionTaskHandler;
-                }
-
-                Folders.SetupFolders();
-
-                ItemSpecifics.CategorySpecificsList = new CustomBindingList<CategorySpecific>();
-                ItemSpecifics.CategorySpecificsList.ListChanged += ItemSpecifics.CategorySpecificsList_ListChanged;
-                ItemSpecifics.CategorySpecificsList.ItemDeleting += ItemSpecifics.CategorySpecificsList_ItemDeleting;
-
-                SetupGUI();
-
-                var configPath = Path.Combine(Folders.Settings, "config.cfg");
-                LoadSettings(configPath);
-
-                WebView.DisableClickSounds();
-
-                Helpers.CheckDotNetVersion();
-
-                _cefBrowserManager = new CefBrowserManager();
-                var filesMissing = _cefBrowserManager.AreRequiredFilesMissing();
-                if (!filesMissing)
-                {
-                    _cefBrowserManager.InitializeCefEngine();
-                }
-
-                _guiChanger = new GuiChanger(Instance, _cefBrowserManager, panelCefBrowser);
-                GridViewEvents.GuiChanger = _guiChanger;
-                repositoryItemComboBoxSite.Items.AddRange(Intl.CountryProvider.GetEbaySiteNameList());
-                repositoryItemComboBoxLocatedIn.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeListWithAny());
-                repositoryItemComboBoxShipsTo.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeList());
-                BuyingService = new BuyingService();
-            }
-            catch (Exception ex)
-            {
-                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
-                Application.Exit();
-            }
-        }
-
-        public Form1(bool bIsFromTrialPrompt)
-        {
-            InitializeComponent();
-            try
-            {
-                if (bIsFromTrialPrompt)
-                {
-                    Folders.SetupFolders();
-                }
-            }
-            catch (Exception ex)
-            {
-                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
-                Application.Exit();
-            }
-        }
-
-        private void Form1_Load(object sender, EventArgs e)
-        {
-            Debug.WriteLine(DateTime.Now.ToString("T") + "**Loading");
-            dockManager1.LayoutVersion = "d3";
-            dockManager1.ForceInitialize();
-            documentManager1.ForceInitialize();
-
-            repositoryItemViews.SelectedIndexChanged += RepositoryItemViews_SelectedIndexChanged;
-            repositoryItemViews.ButtonClick += RepositoryItemViews_ButtonClick;
-
-            UserLookAndFeel.Default.ActiveLookAndFeel.StyleChanged += ActiveLookAndFeel_StyleChanged;
-            ChangePanelHeader();
-            SetTreeListCheckboxesState();
-        }
-
-        private void Form1_Shown(object sender, EventArgs e)
-        {
-            Debug.WriteLine(DateTime.Now.ToString("T") + "**Showing");
-            LoadWorkspacesOnFormLoad();
-            ApplyWorkSpaceOnFormShown();
-            var obsoletePanel = dockManager1.Panels.ToList().FirstOrDefault(p => p.ID == new Guid("92581858-ecdc-4d88-be24-9bb7948442dc"));
-            obsoletePanel?.Dispose();
-            ResultsView.TogglePanelsVisibility();
-            dockManager1.EndUpdate();
-
-            _1SecTimer = new System.Windows.Forms.Timer();
-            _1SecTimer.Tick += _1secTimer_Elapsed;
-            _1SecTimer.Interval = (int)TimeSpan.FromSeconds(1).TotalMilliseconds;
-            _1SecTimer.Tag = 0;
-            _1SecTimer.Start();
-
-            #region Licensing
-
-            LicenseUtility = new LicenseUtility();
-            LicenseUtility.SetDefaultLimits();
-
-            InitUpdater();
-            ImageCleaner.StartCleanUpTimer();
-            //after license completed
-            var fromCurrentSynchronizationContext = TaskScheduler.FromCurrentSynchronizationContext();
-            LicenseUtility
-                .CheckLicense()
-                .ContinueWith(_ =>
-                    ConnectionConfig.SetApiConfig()
-                    .ContinueWith(_ =>
-                    {
-                        barStaticLicense.Caption = LicenseUtility.GetLicenseStatusText();
-                        Loggers.InitGoogleAnalyticsV4(ProgramState.SerialNumberShort, LicenseUtility.CurrentSubscriptionType);
-
-                        ServicePointManager.ServerCertificateValidationCallback = Helpers.BuyItNowConfirmation;
-                        if (!ConnectionConfig.TradingAPIEnabled)
-                        {
-                            barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
-                        }
-
-                        ToggleCheckoutPermission();
-
-                        CookieManager.ReadChromeVersion();
-                        var profileListLoaded = AssignProfileCombobox();
-                        if (ConnectionConfig.CheckoutEnabled && profileListLoaded)
-                        {
-                            barCheckImmediatePayment.Checked = CreditCardService.CreditCardPaymentEnabled;
-                        }
-                        else
-                        {
-                            barCheckImmediatePayment.Checked = false;
-                            CreditCardService.CreditCardPaymentEnabled = false;
-                        }
-
-                        if (ConnectionConfig.SkipBuyConfirmation)
-                        {
-                            barCheckNoConfirmations.Checked = UserSettings.SkipBuyConfirmation;
-                        }
-                        else
-                        {
-                            barCheckNoConfirmations.Checked = false;
-                            UserSettings.SkipBuyConfirmation = false;
-                        }
-
-                        LicenseUtility.LicenseCheckCompleted = true;
-                    }, fromCurrentSynchronizationContext));
-
-            #endregion Licensing
-
-            InitBGtasks();
-            _viewReporter = new ViewReporter(this);
-
-            Log.Info("Loaded version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ProgramState.SerialNumber);
-            //if (!webBrowser1.IsDisposed)webBrowser1.Navigate($"https://ubuyfirst.com/uBuyFirst/description.html?ver={ProgramState.UBFVersion}&license={ProgramState.SerialNumber}&hwid={ProgramState.HWID}&cb={new Random().Next(10000)}");
-
-            barCheckItemSoundAlert.CheckedChanged += barCheckItemSoundAlert_CheckedChanged;
-            galleryControl1.MouseLeave += _focusRouter.FocusBrowser;
-            lstchkXfilterList.ItemCheck += lstchkXfilterList_ItemCheck;
-
-            barStaticErrorsVal.Visibility = BarItemVisibility.Always;
-            if (Program.Sandbox)
-            {
-                Text = "Sandbox mode";
-                linkeBayPrivacyPolicy.Visible = true;
-                linkeBayUserAgreement.Visible = true;
-                linkReportItem.Visible = true;
-                picBoxEbayLogo.Visible = true;
-            }
-
-            if (Config.SafeEnabled)
-            {
-                linkeBayPrivacyPolicy.Visible = true;
-                linkeBayUserAgreement.Visible = true;
-                linkReportItem.Visible = true;
-                picBoxEbayLogo.Visible = true;
-            }
-
-            InitEbayAccounts();
-
-            _searchService = new SearchService(_requestsCfg, LicenseUtility, _viewReporter);
-
-            if (barCheckItemAutoStartSearch.Checked || Upgrader.ResumeSearchAfterUpgrade)
-            {
-                barButtonStart.PerformClick();
-            }
-
-            _synchronizationContext.Send(state => SaveSettings(), null);
-            int newHeight = Convert.ToInt32(16 * DpiProvider.Default.DpiScaleFactor + 2);
-            if (treeList1.RowHeight > 0 && treeList1.RowHeight < newHeight)
-            {
-                treeList1.RowHeight = newHeight;
-            }
-
-            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
-
-            _1HourTimer = new System.Windows.Forms.Timer();
-            _1HourTimer.Tick += _1HourTimer_Elapsed;
-            var timerInterval = 55;
-            _1HourTimer.Interval = (int)TimeSpan.FromMinutes(timerInterval).TotalMilliseconds;
-            _1HourTimer.Tag = 0;
-            _1HourTimer.Start();
-
-            _searchTermSyncTimer = new System.Windows.Forms.Timer();
-
-            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
-            _searchTermSyncTimer.Tick += _SearchTermSyncTimer_Elapsed;
-
-            Debug.WriteLine(DateTime.Now.ToString("T") + "**Shown");
-
-            if (Debugger.IsAttached || ProgramState.SerialNumber.StartsWith("ROMA"))
-                Program.AffiliateOff = true;
-            if (Program.FirstVisit)
-            {
-                //Analytics.AddEvent("", "first_visit", 1);
-                //Analytics.PostReport();
-            }
-        }
-
-        private bool AssignProfileCombobox()
-        {
-            var profileNames = CookieManager.GetProfileNamesFirefox();
-
-            // Clear existing items and add new ones.
-            repositoryItemComboBoxProfile.Items.Clear();
-            repositoryItemComboBoxProfile.Items.AddRange(profileNames);
-
-            // Check if there are any items in the ComboBox.
-            if (repositoryItemComboBoxProfile.Items.Count == 0)
-            {
-                return false;
-            }
-
-            // Determine which item to select in the ComboBox.
-            CookieProfile selectedItem;
-
-            if (repositoryItemComboBoxProfile.Items.Count == 1)
-            {
-                // If there is only one item in the ComboBox, select that item.
-                selectedItem = repositoryItemComboBoxProfile.Items[0] as CookieProfile;
-            }
-            else
-            {
-                // If there are multiple items, find the first one that matches the current profile.
-                var currentProfile = CookieManager.Profile;
-                selectedItem = repositoryItemComboBoxProfile.Items.OfType<CookieProfile>().FirstOrDefault(item => item.Profile == currentProfile?.Profile);
-            }
-
-            // If a matching profile is found or there's only one profile, set it as the edit value.
-            if (selectedItem != null)
-            {
-                barEditItemProfile.EditValue = selectedItem;
-                return true;
-            }
-
-            // Fallback: Set the first item as the edit value if no specific match is found.
-            barEditItemProfile.EditValue = repositoryItemComboBoxProfile.Items[0];
-            return true;
-        }
-
-        internal void ReporterDotsProgressChanged(object sender, byte e)
-        {
-            ShowDebugStats();
-            RunningDots();
-        }
-
-        internal void _reporterItem_ProgressChanged(object sender, FoundItem e)
-        {
-            HandleNewItem(e);
-        }
-
-        internal void ReportLogTxt(object sender, string e)
-        {
-            memoEditLog.Text += $"\r\n{e}";
-        }
-
-        private void BrowserDocCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
-        {
-            var webBrowser = (WebBrowser)sender;
-            if (webBrowser.DocumentText.Length > 0)
-            {
-                try
-                {
-                    WebView.SetZoom(webBrowser, UserSettings.BrowserFontSize);
-                }
-                catch (Exception ex)
-                {
-                    if (ProgramState.Isdebug)
-                        MessageBox.Show(ex.Message);
-                }
-            }
-        }
-
-        private void MouseMoveResetIdle(object sender, MouseEventArgs e)
-        {
-            ProgramState.Idlesw.Restart();
-        }
-
-        private void Dgv1KeyDown(object sender, KeyEventArgs e)
-        {
-            var grView = (AdvBandedGridView)sender;
-            try
-            {
-                if (e.KeyCode == Keys.Delete)
-                {
-                    if (grView.RowCount == 0)
-                    {
-                        _guiChanger.ClearItemInfoOnZeroRows();
-                    }
-
-                    e.Handled = true;
-                }
-            }
-            catch (Exception ex)
-            {
-                ExM.ubuyExceptionHandler("dgv1KeyDown: ", ex);
-            }
-        }
-
-        [Obfuscation(Exclude = false,
-            Feature = "preset(maximum);"
-                      + "+anti debug;"
-                      + "+anti dump;"
-                      + "+anti ildasm;"
-                      + "+anti tamper(key=dynamic);"
-                      + "+constants;"
-                      + "+ctrl flow;"
-                      + "+invalid metadata;"
-                      + "+ref proxy;"
-                      + "-rename;")]
-
-        #region eBay Accounts
-
-        private static async Task IsTokenValid(EbayAccount account)
-        {
-            try
-            {
-                if (string.IsNullOrEmpty(account?.TokenPo) || Program.Sandbox)
-                    return;
-
-                var apiContextPlaceOffer = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, account.TokenPo);
-
-                var tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
-                if (tokenStatus == null)
-                {
-                    tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
-                }
-
-                if (tokenStatus?.Status != TokenStatusCodeType.Active)
-                {
-                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
-                    EBayAccountsList.Remove(account);
-                }
-            }
-            catch (ApiException ex)
-            {
-                if (string.IsNullOrEmpty(account?.UserName))
-                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
-                else
-                    XtraMessageBox.Show($"Account: {account.UserName}\nRemoved from account list\nToken status: {ex.Message}");
-
-                EBayAccountsList.Remove(account);
-            }
-
-            catch (Exception ex)
-            {
-                ExM.ubuyExceptionHandler("isTokenValid: ", ex);
-            }
-        }
-
-        private void AddEbayAccount()
-        {
-            if (EBayAccountsList.Count > 0 && !LicenseUtility.CurrentLimits.MultipleEbayAccountsEnabled)
-            {
-                XtraMessageBox.Show(En_US.Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_);
-
-                return;
-            }
-
-            var account = ShowAuthorizeOnEbay();
-            if (account != null)
-            {
-                var isNewAccount = true;
-                for (var i = 0; i < EBayAccountsList.Count; i++)
-                {
-                    if (EBayAccountsList[i].UserName == account.UserName)
-                    {
-                        EBayAccountsList[i] = account;
-                        isNewAccount = false;
-
-                        break;
-                    }
-                }
-
-                if (isNewAccount)
-                {
-                    EBayAccountsList.Add(account);
-                    Analytics.AddEvent("", "EbayAccountAdded", 1);
-                }
-            }
-
-            SaveSettings();
-        }
-
-        public static EbayAccount ShowAuthorizeOnEbay(EbayAccount editAccount = null)
-        {
-            //AutoMeasurement.Client.TrackScreenView("Screen - Authorize on ebay");
-
-            var authenticator = new Authenticator(editAccount);
-            var authForm = new FormAuth(editAccount, authenticator);
-
-            var result = authForm.ShowDialog();
-            if (result == DialogResult.OK)
-            {
-                if (!string.IsNullOrEmpty(authForm.EbayAccount.RefreshToken))
-                {
-                    return authForm.EbayAccount;
-                }
-
-                if (string.IsNullOrEmpty(authForm.EbayAccount.UserName) || string.IsNullOrEmpty(authForm.EbayAccount.TokenPo))
-                {
-                    return null;
-                }
-
-                return authForm.EbayAccount;
-            }
-
-            return null;
-        }
-
-        #endregion eBay Accounts
-
-        private bool isShutdownInitiated = false;
-
-        private async void ClosingMainForm(object sender, FormClosingEventArgs e)
-        {
-            // Check if shutdown has already been initiated
-            if (isShutdownInitiated)
-            {
-                // Shutdown is already in progress, so do nothing
-                return;
-            }
-
-            // Indicate that shutdown process has been initiated
-            isShutdownInitiated = true;
-
-            // Prevent the form from closing immediately
-            e.Cancel = true;
-            Cursor.Current = Cursors.WaitCursor;
-            Text = "uBuyFirst - Shutting down";
-
-            try
-            {
-                // Disconnect from services asynchronously
-                _ablyClient.BeDisconnected(); // Ensure this is an async method
-                await _mqttManager.Disconnect(); // Ensure this is an async method
-
-                // Attempt to stop other background work with a timeout
-                var stopTask = StopWorking(); // Assuming StopWorking is an async Task method
-                var delayTask = Task.Delay(TimeSpan.FromSeconds(60));
-                await Task.WhenAny(stopTask, delayTask);
-
-                // Additional cleanup or checks can be performed here
-            }
-            catch (Exception ex)
-            {
-                // Log the exception or show a message to the user
-                MessageBox.Show($"An error occurred while shutting down: {ex.Message}", "Shutdown Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
-            }
-            finally
-            {
-                // Complete the shutdown process by closing the form without further cancellation
-                // Perform this action on the UI thread
-
-                // Check if the form's handle is created before invoking
-                if (this.IsHandleCreated)
-                {
-                    this.Invoke(new Action(() =>
-                    {
-                        // It's safe to close the form now as the shutdown process has been completed or acknowledged with an error
-                        this.Close(); // This call will not re-enter the closing logic due to the isShutdownInitiated flag
-                    }));
-                }
-                else
-                {
-                    // Handle the case where the form's handle is not created
-                    // Log this situation if necessary
-                    // Ensure the program exits
-                    Environment.Exit(0);
-                }
-            }
-        }
-
-        internal void ShowActivationWindow()
-        {
-            var dialog = new FormSubscription();
-            dialog.ShowDialog(Instance);
-
-            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
-            if (dialog.DialogResult != DialogResult.OK)
-                return;
-
-            Cursor.Current = Cursors.WaitCursor;
-            var ipManagerFolder = "C:\\ProgramData\\IPManager";
-            if (System.IO.Directory.Exists(ipManagerFolder))
-                System.IO.Directory.GetFiles(ipManagerFolder, "*").ToList().ForEach(System.IO.File.Delete);
-            LicenseUtility.TryDownloadExistingLicense(dialog.SerialNumber);
-
-            try
-            {
-                var license = LicenseUtility.ReadGenuineLicenseFromFile();
-
-                if (license == null)
-                {
-                    LicenseUtility.ActivateLicense(dialog.SerialNumber);
-                }
-                else
-                {
-                    XtraMessageBox.Show(En_US.Form1_ShowActivationWindow_Please__restart_the_application__);
-                }
-            }
-            catch (Exception)
-            {
-                //ignored
-            }
-
-            Cursor.Current = Cursors.Default;
-        }
-
-        public static void ShowCustomColumnsWindow(object sender, EventArgs e)
-        {
-            var dialog = new FormSpecificsColumns();
-            dialog.ShowDialog();
-        }
-
-        private void zoomTrackBarControl1_EditValueChanged_1(object sender, EventArgs e)
-        {
-            galleryControl1.Gallery.Groups.Last().Gallery.ImageSize = new Size(zoomTrackBarPictures.Value, zoomTrackBarPictures.Value);
-            LastSliderValueSmall = zoomTrackBarPictures.Value;
-        }
-
-        private void barButtonClear_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            _guiChanger.ClearFoundItems();
-        }
-
-        private void barButtonReport_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            if (_formReportDialog != null)
-            {
-                _formReportDialog.WindowState = FormWindowState.Normal;
-                _formReportDialog.Focus();
-            }
-
-            else
-            {
-                _formReportDialog = new FormReportDialog();
-                // formReportDialog.MdiParent = this;
-                _formReportDialog.FormClosed += (o, ea) => { _formReportDialog = null; };
-                _formReportDialog.Show();
-            }
-
-            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
-        }
-
-        private void barButtonBuy_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
-
-            if (selectedRows.Length == 0)
-                return;
-
-            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
-
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
-        }
-
-        private void barButtonSubscriptionInfo_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            ShowActivationWindow();
-        }
-
-        private void barButtonItem3_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var dialog = new FormAbout();
-            dialog.ShowDialog();
-        }
-
-        private void simpleButton1_Click(object sender, EventArgs e)
-        {
-            Stat._errorsCount = 0;
-            memoEditLog.Text = "";
-            barStaticErrorsVal.Caption = "Errors: 0";
-        }
-
-        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
-        {
-            Process.Start(hyperlinkLabelFolder.Tag.ToString());
-        }
-
-        private void dockDescription_CustomButtonClick(object sender, ButtonEventArgs e)
-        {
-            if (e.Button.Properties.Tag.ToString() == "BackgroundColor")
-            {
-                var result = colorDialog1.ShowDialog();
-                if (result == DialogResult.OK)
-                {
-                    // Set form background to the selected color.
-                    UserSettings.BrowserBg = WebView.HexConverter(colorDialog1.Color);
-                    _guiChanger.SetBrowserDocumentText("");
-                }
-            }
-
-            if (e.Button.Properties.Tag.ToString() == "IncreaseFont")
-            {
-                UserSettings.BrowserFontSize += 10;
-
-                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
-            }
-
-            if (e.Button.Properties.Tag.ToString() == "DecreaseFont")
-            {
-                UserSettings.BrowserFontSize = UserSettings.BrowserFontSize - 10;
-                if (UserSettings.BrowserFontSize < 10)
-                    UserSettings.BrowserFontSize = 10;
-
-                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
-            }
-        }
-
-        #region Browser
-
-        private void zoomTrackBarBrowser_EditValueChanged(object sender, EventArgs e)
-        {
-            if (sender is ZoomTrackBarControl zoomTrackBar)
-            {
-                UserSettings.BrowserFontSize = zoomTrackBar.Value;
-                if (webBrowser1.DocumentText.Length > 0)
-                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
-            }
-
-            if (sender is BarEditItem repositoryZoomTrackBar)
-            {
-                UserSettings.BrowserFontSize = int.Parse(repositoryZoomTrackBar.EditValue.ToString());
-                if (webBrowser1.DocumentText.Length > 0)
-                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
-            }
-        }
-
-        private void colorPickBrowser_EditValueChanged(object sender, EventArgs e)
-        {
-            if (sender is BarEditItem colorEdit)
-            {
-                UserSettings.BrowserBg = WebView.HexConverter((Color)colorEdit.EditValue);
-                btnBrowserSettings.Appearance.BackColor = (Color)colorEdit.EditValue;
-                _guiChanger.SetBrowserDocumentText("");
-            }
-
-            if (sender is ColorPickEdit colorPickEdit)
-            {
-                UserSettings.BrowserBg = WebView.HexConverter(colorPickEdit.Color);
-                btnBrowserSettings.Appearance.BackColor = colorPickEdit.Color;
-                _guiChanger.SetBrowserDocumentText("");
-            }
-        }
-
-        #endregion
-
-
-        private static async void _1secTimer_Elapsed(object sender, EventArgs e)
-        {
-            var timer = (System.Windows.Forms.Timer)sender;
-            var elapsedSec = (int)timer.Tag + 1;
-            timer.Tag = elapsedSec;
-
-            RowStatusUpdater.RefreshRowsStatus();
-
-            if (DateTime.UtcNow.Second % 3 == 0)
-                _synchronizationContext.Send(state => Instance.ShowDebugStats(), null);
-
-            if (ProgramState.Idlesw.Elapsed.TotalSeconds > 20 && Upgrader.ImmediateUpgradeAvailable)
-            {
-                Upgrader.ImmediateUpgradeAvailable = false;
-                Upgrader.UpgradeImmediately();
-            }
-
-            GoogleAnalytics?.SendSearchStatsPeriodic();
-
-            var needRestart = Instance._ablyClient.NeedRestart();
-
-            if ((elapsedSec + 15) % 60 == 0 && needRestart)
-            {
-                Debug.WriteLine($"Ably: {elapsedSec} Force disconnect");
-                Instance._ablyClient.Disconnect();
-            }
-
-            if (elapsedSec % 60 == 0 && needRestart)
-            {
-                Debug.WriteLine($"Ably: {elapsedSec} Force connect");
-                if (!ConnectionConfig.MQTTEnabled)
-                    Instance.StartPushClients();
-            }
-        }
-
-        private void _1HourTimer_Elapsed(object sender, EventArgs e)
-        {
-            _updater?.CheckProgramUpdateAvailable();
-            ConnectionConfig.SetApiConfig().ConfigureAwait(false);
-            if (!ConnectionConfig.TradingAPIEnabled)
-            {
-                barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
-            }
-
-            LicenseUtility.PeriodicLicenseCheck();
-        }
-
-        private async void _SearchTermSyncTimer_Elapsed(object sender, EventArgs e)
-        {
-            if (!UserSettings.SyncSearchTermsEnabled)
-                return;
-
-
-            var url = UserSettings.SyncSearchTermsUrl;
-            if (string.IsNullOrEmpty(url))
-                return;
-
-            if (Regex.IsMatch(url, SearchTermFetcher.GoogleSpreadsheetRegex))
-                url = SearchTermFetcher.GetCsvExportUrl(url);
-
-            var filePath = await SearchTermFetcher.DownloadKeywordsFileAsync(url);
-            if (string.IsNullOrEmpty(filePath))
-                return;
-
-            var searchTermsFileContent = File.ReadAllText(filePath);
-            var searchTermFileHash = Helpers.Str2Guid(searchTermsFileContent).ToString();
-            if (searchTermFileHash != UserSettings.SyncSearchTermsFileHash)
-            {
-                UserSettings.SyncSearchTermsFileHash = searchTermFileHash;
-                ImportKeywordsFromFile(filePath);
-                if (_searchService is { Running: true })
-                {
-                    await StopWorking();
-                    StartWorking();
-                }
-            }
-        }
-
-        private async void InitEbayAccounts()
-        {
-            await Task
-                .Run(async () =>
-                {
-                    var ebayAccounts = EBayAccountsList.ToList();
-                    foreach (var ebayAccount in ebayAccounts)
-                    {
-                        await IsTokenValid(ebayAccount).ContinueWith(t => Debug.WriteLine("Account checked"));
-                    }
-
-                    EBayAccountInitCompleted = true;
-                })
-                .ContinueWith(t => { });
-
-            Debug.WriteLine("EBayAccountInitCompleted = true");
-        }
-
-        private void InitUpdater()
-        {
-            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Never;
-            _updater = new Updater();
-        }
-
-        private void CreateDefaultSearches()
-        {
-            if (_ebaySearches.ChildrenCore.Count == 0)
-            {
-                NewEbaySearch("iPhone", "iPhone 14 -(128,256,128gb,256gb) -verizon", "9355");
-                NewEbaySearch("GPU 4090", "4090 (MSI,Asus,Gigabyte)", "175673");
-                NewEbaySearch("Lenovo laptop", "Lenovo ThinkPad, Lenovo IdeaPad", "175672");
-                SetTreeListCheckboxesState();
-            }
-
-            for (var i = 0; i < treeList1.Nodes.Count; i++)
-                SetNodeChecked(treeList1.Nodes[i]);
-        }
-
-        private async void InitBGtasks()
-        {
-            _checkItemsStatus = new GetItemsStatus();
-            _checkItemsStatus.StartCheckStatusLoop();
-            StartTelegram();
-            StartPushbullet();
-            barStaticItemTimeDiffText.Caption = $@"eBay - Local time: {await Task.Run(TimeSync.GetTimeSyncDifference)} sec";
-        }
-
-        private void StartPushbullet()
-        {
-            if (string.IsNullOrEmpty(_pushbullet?.Token))
-                return;
-
-            PushBulletListen();
-        }
-
-        private void StartTelegram()
-        {
-            if (string.IsNullOrEmpty(_telegramSender?.TelegramBotID))
-                return;
-
-            _telegramSender?.StartListening();
-        }
-
-        private static async void GetPublicIp()
-        {
-            await Task
-                .Run(() =>
-                {
-                    ProgramState.PublicIp = NetTools.FetchUrl("http://bot.whatismyipaddress.com/");
-                    if (string.IsNullOrEmpty(ProgramState.PublicIp))
-                    {
-                        ProgramState.PublicIp = "************";
-                    }
-                })
-                .ConfigureAwait(false);
-        }
-
-        private void SetupGUI()
-        {
-            layoutControl1.SetDefaultLayout();
-            layoutControlForPicture.SetDefaultLayout();
-            ribbonControl1.Minimized = true;
-            barStaticBuildVersion.Caption = En_US.Form1_Form1_Build__v_ + ProgramState.UBFVersion;
-            hyperlinkLabelFolder.Text = $"App folder: {Folders.Settings}";
-            hyperlinkLabelFolder.Tag = $"{Folders.Settings}";
-            //if (!_isdebug)
-            splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
-            if (Program.AffiliateOff)
-                barStaticErrorsVal.Caption = "Affiliate Off";
-        }
-
-        private void ActiveLookAndFeel_StyleChanged(object sender, EventArgs e)
-        {
-            ChangePanelHeader();
-
-            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
-            foreach (var grView in uniqGrids)
-            {
-                FormatRuleManager.ApplyGridFormatRules(grView);
-            }
-        }
-
-        private static void ChangePanelHeader()
-        {
-            var panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindow];
-            if (panelHeader != null)
-            {
-                panelHeader.ContentMargins.Top = 1;
-                panelHeader.ContentMargins.Bottom = 1;
-            }
-
-            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinTabHeader];
-            if (panelHeader != null)
-            {
-                panelHeader.ContentMargins.Top = 1;
-                panelHeader.ContentMargins.Bottom = 1;
-            }
-
-            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindowBorder];
-
-            panelHeader.ContentMargins.Top = 1;
-            panelHeader.ContentMargins.Bottom = 1;
-            foreach (DockPanel panel in Instance.dockManager1.Panels)
-            {
-                panel.Invalidate();
-                panel.Refresh();
-            }
-
-            LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
-        }
-
-        private void toolTipController2_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
-        {
-            ShowTooltipThumbnal(e);
-        }
-
-        private void ShowTooltipThumbnal(ToolTipControllerGetActiveObjectInfoEventArgs e)
-        {
-            if (e.SelectedControl != gridControl1)
-                return;
-
-            ToolTipControlInfo info = null;
-            var sTooltip1 = new SuperToolTip();
-            try
-            {
-                if (!(gridControl1.GetViewAt(e.ControlMousePosition) is GridView view))
-                    return;
-
-                var hi = view.CalcHitInfo(e.ControlMousePosition);
-                if (hi.HitTest == GridHitTest.RowCell && hi.Column.FieldName == "Thumbnail")
-                {
-                    info = new ToolTipControlInfo(new CellToolTipInfo(hi.RowHandle, hi.Column, "cell"), hi.RowHandle + "," + hi.Column);
-                    if (view.GetRowCellValue(hi.RowHandle, "Thumbnail") is Bitmap cellIm)
-                    {
-                        var item1 = new ToolTipItem();
-                        item1.Image = cellIm;
-                        sTooltip1.Items.Add(item1);
-                    }
-
-                    info = new ToolTipControlInfo(hi.RowHandle + "," + hi.Column, "");
-                    info.SuperTip = sTooltip1;
-                }
-            }
-            catch (Exception)
-            {
-                // ignored
-            }
-            finally
-            {
-                e.Info = info;
-            }
-        }
-
-        #region InfoPanel
-
-        private void lcItemID_Click(object sender, EventArgs e)
-        {
-            object tag;
-            if (!(sender is LabelControl labelControl))
-            {
-                if (!(sender is LayoutControlItem layoutControlItem))
-                    return;
-
-                tag = layoutControlItem.Tag;
-            }
-            else
-            {
-                tag = labelControl.Tag;
-            }
-
-            var ebayAccount = ((DataList)tag)?.EbayAccount;
-            if (ebayAccount != null)
-            {
-                var url = ((DataList)tag).GetAffiliateLink();
-                if (url != null)
-                    Browser.OpenAffiliateLink((DataList)tag);
-            }
-        }
-
-        private void dockItemProperties_CustomButtonClick(object sender, ButtonEventArgs e)
-        {
-            if (e.Button.Properties.Tag.ToString() == "ItemDesciptionOptions")
-            {
-                popupMenuItemDetails.ShowPopup(MousePosition);
-            }
-        }
-
-        private void SetLayoutControlFont(Font f, int delta)
-        {
-            if (layoutControl1.Root.AppearanceItemCaption.FontSizeDelta < 0)
-                layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = 0;
-
-            layoutControl1.Root.AppearanceItemCaption.Font = new Font(f.FontFamily, (float)8.25, f.Style);
-            layoutControl1.Appearance.Control.FontSizeDelta = delta;
-            lcTitle.Font = new Font(lcTitle.Font.FontFamily, layoutControl1.Appearance.Control.Font.Size + 3, FontStyle.Bold);
-        }
-
-        private void btnEditItemProperties_Click(object sender, EventArgs e)
-        {
-            popupMenuItemDetails.ShowPopup(MousePosition);
-        }
-
-        private void btnEditPictureProperties_Click(object sender, EventArgs e)
-        {
-            if (lcipanelPicturesSettingControl.Visibility != DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
-            {
-                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
-            } 
-            else
-            {
-                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
-            }
-        }
-
-        #endregion
-
-        #region Buying buttons
-
-        private void barButtonMakeOffer_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
-
-            if (selectedRows.Length == 0)
-                return;
-
-            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
-
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
-        }
-
-        private void btnMakeOffer_Click(object sender, EventArgs e)
-        {
-            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
-
-            if (selectedRows.Length == 0)
-                return;
-
-            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
-
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
-        }
-
-        private void panelBuyButton_Click_1(object sender, EventArgs e)
-        {
-            if (FocusRouter.FocusedGridView == null)
-            {
-                return;
-            }
-
-            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
-
-            if (selectedRows.Length == 0)
-                return;
-
-            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
-
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
-        }
-
-        #endregion
-
-        private void barButtonClear_ItemDoubleClick(object sender, ItemClickEventArgs e)
-        {
-            var dialogResult = XtraMessageBox.Show(this, "Clear results?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
-            if (dialogResult == DialogResult.Yes)
-            {
-                ClearOldResults();
-                barButtonClear.Caption = "Clear results " + DateTime.Now.ToString("HH:mm:ss");
-            }
-        }
-
-        private void ClearOldResults()
-        {
-            _guiChanger.ClearFoundItems();
-            SearchService.Old.Clear();
-            CacheManager.ClearCache();
-            _ebaySearches.ChildrenCore.ForEach(s => s.InitialSearchCount = 0);
-            _telegramSender?.ClearQueue();
-        }
-
-        private void repositoryItemCheckedComboBoxEditCondition_CustomDisplayText(object sender,
-            CustomDisplayTextEventArgs e)
-        {
-            var term = e.Value as string;
-            if (string.IsNullOrEmpty(term))
-            {
-                e.DisplayText = "Any condition";
-            }
-        }
-
-        private void btnClearFilter_Click(object sender, EventArgs e)
-        {
-            popupContainerControl1.OwnerEdit?.CancelPopup();
-        }
-
-        private void barStaticErrorsVal_ItemDoubleClick(object sender, ItemClickEventArgs e)
-        {
-            //XtraMessageBox.Show("Directx: " + GridDirectXEnabled(gridControl1).ToString());
-            if (splitContainerControl2.PanelVisibility == SplitPanelVisibility.Both)
-            {
-                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
-            }
-            else
-            {
-                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Both;
-            }
-        }
-
-        private void UpdateRequestCfg()
-        {
-            if (!Visible)
-                return;
-
-            _requestsCfg.FindReqMaxThreads = Convert.ToInt32(spinEditFindReqMaxThreads.Value);
-            if (_requestsCfg.GetItemDetailsReqMaxThreads != Convert.ToInt32(spinEditGetItemDetailsMaxThreads.Value))
-            {
-                _requestsCfg.GetItemDetailsReqMaxThreads = Convert.ToInt32(spinEditGetItemDetailsMaxThreads.Value);
-                _getItemQueueLimiter = new SemaphoreSlim(_requestsCfg.GetItemDetailsReqMaxThreads);
-                BrowseAPIParser.BrowseAPIGetItemQueueLimiter = new SemaphoreSlim(_requestsCfg.GetItemDetailsReqMaxThreads);
-            }
-
-            _requestsCfg.DownloadAvatars = chkDownloadAvatars.Checked;
-            _requestsCfg.DownloadOtherImages = chkDownloadOtherImages.Checked;
-            _requestsCfg.DownloadDescription = chkDownloadDescription.Checked;
-            _requestsCfg.UpdateItemStatus = chkUpdateItemStatusFor2Min.Checked;
-            _requestsCfg.EnabledApi = checkUseAPI.Checked;
-            _requestsCfg.EnabledRss = checkUseRSS.Checked;
-            _requestsCfg.EnabledRss2 = checkUseRSS2.Checked;
-            _requestsCfg.ShowSoldItems = checkEditShowSoldItems.Checked;
-            _requestsCfg.WhiteSpaceAnalyzer = checkEditWhitespaceTokenizer.Checked;
-            if (!_requestsCfg.EnabledApi && !_requestsCfg.EnabledRss && !_requestsCfg.EnabledRss2)
-            {
-                _requestsCfg.EnabledApi = true;
-                MessageBox.Show("Please, enable at least one option - API, RSS or RSS2");
-            }
-        }
-
-        private void _requestCfgChanged(object sender, EventArgs e)
-        {
-            UpdateRequestCfg();
-            SetWhiteSpaceAnalyzer(_requestsCfg.WhiteSpaceAnalyzer);
-        }
-
-        private static void SetWhiteSpaceAnalyzer(bool requestsCfgWhiteSpaceAnalyzer)
-        {
-            KeywordHelpers.SetAnalyzerType(requestsCfgWhiteSpaceAnalyzer);
-        }
-
-        private void Info_Click(object sender, EventArgs e)
-        {
-            XtraMessageBox.Show(@"Find request speed - How many find request to eBay servers per second uBuyFirst actually makes
-
-GetItem queue - after uBuyFirst found a new item, it needs to make one more request to eBay server to get this item's details. If there are many new items, they placed into a queue.
-
-GetItem threads - how many parallel GetItem details requests uBuyFirst can make.
-
-Thumbs - placed into a queue. You may disable it if you don't need them.
-
-Other images - gallery images. You may disable it if you don't need them.
-
-Status update - after new item were added to a grid uBuyFirst checks for 2 minutes whetheer the item where sold. 20 items updates per request. If you have 10 items in queue then 10x20=200 items currently updating its status.
-
-Download description - you may uncheck this if you don't use Description in your filters and Sub Searches.
-
-Use API, RSS, RSS2 - we can look for new items in these 3 sources. Sometimes some of them faster then another.
-
-Show sold items - look in the sold items archive for items that were sold before ubuyfirst found them.
-");
-        }
-
-        #region Gallery
-
-        private void galleryControl1_Gallery_ItemDoubleClick(object sender, GalleryItemClickEventArgs e) => _picManager.GetLargeImages(e.Item, this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);
-
-        private void galleryControl1_Gallery_GalleryItemHover(object sender, GalleryItemEventArgs e)
-        {
-            var galleryItem = e.Item;
-            if (e.Item.Hint == "Error")
-            {
-                var galleryItemGroupCollection = ((GalleryControlGallery)sender).GalleryControl.Gallery.Groups;
-                var galleryItemGroup = galleryItemGroupCollection.Last();
-                var galleryItemCollection = galleryItemGroup.Items;
-                var pictureUrls = new List<string>();
-                foreach (GalleryItem? item in galleryItemCollection)
-                {
-                    var picUrl = item?.Tag?.ToString();
-                    if (!string.IsNullOrEmpty(picUrl))
-                        if (picUrl != null)
-                        {
-                            pictureUrls.Add(picUrl);
-                        }
-                }
-
-                GridViewEvents.GuiChanger.ClearGalleryControl();
-                ImageCache.Inst.ClearCache();
-                GridViewEvents.GuiChanger.UpdatePicPanel(pictureUrls.ToList(), new CancellationToken(false));
-            }
-
-
-            _picManager.GetLargeImages(galleryItem,this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);
-        }
-
-        #endregion
-
-        #region Tray Icon
-
-        private void TrayIcon_Click(object sender, MouseEventArgs e)
-        {
-            _trayManager.SetTrayLabel();
-            if (e.Button == MouseButtons.Left)
-                _trayManager.SwitchMinimizedState();
-        }
-
-        private void toolStripMenuItemExit_Click(object sender, EventArgs e)
-        {
-            Application.Exit();
-        }
-
-        private void toolStripMenuItemShow_Click(object sender, EventArgs e)
-        {
-            _trayManager.SwitchMinimizedState();
-        }
-
-        #endregion Tray Icon
-
-        public void ShowUpdateNotification()
-        {
-            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Always;
-        }
-
-        private void BarButtonRestartOnUpdate_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            Upgrader.UpgradeImmediately();
-        }
-
-        private void hyperLinkLabel_Click(object sender, EventArgs e)
-        {
-            var label = (HyperlinkLabelControl)sender;
-            Process.Start(label.Tag.ToString());
-        }
-
-        private void repositoryItemViews_AddingMRUItem(object sender, AddingMRUItemEventArgs e)
-        {
-            return;
-
-            if (UserSettings.CanShowEbaySearchEditor)
-                return;
-
-            if (e.AddReason == MRUItemAddReason.Internal)
-                return;
-
-            if (!(sender is MRUEdit mruEdit))
-                return;
-
-            if (!(mruEdit.Parent is TreeList treeList))
-                return;
-
-            if (!(treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find))
-                return;
-
-            var text = $"Create '{e.Item}' View?\nItems for this eBay search will be displayed in this View";
-            var dialogResult = XtraMessageBox.Show(treeList, text, @"Create View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
-            if (dialogResult != DialogResult.Yes)
-            {
-                e.Cancel = true;
-                mruEdit.SelectedItem = focusedKw2Find.ViewName;
-                mruEdit.CancelPopup();
-                treeList1.CancelCurrentEdit();
-                MruManager.DisableEdit(repositoryItemViews);
-
-                return;
-            }
-
-            UserSettings.CanShowEbaySearchEditor = true;
-            var newViewName = e.Item.ToString();
-            ResultsView.CreateView(newViewName);
-            GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
-            UserSettings.CanShowEbaySearchEditor = false;
-            ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);
-            MruManager.DisableEdit(repositoryItemViews);
-        }
-
-        private void repositoryItemViews_RemovingMRUItem(object sender, RemovingMRUItemEventArgs e)
-        {
-            string removedViewName = e.Item.ToString();
-            if (removedViewName == "Results")
-            {
-                XtraMessageBox.Show(this, "Sorry, you can't remove default view 'Results'");
-                e.Cancel = true;
-                return;
-            }
-
-            var text = $"Delete '{e.Item}' View?\nAll eBay Searches in this view will be assigned to 'Results' View";
-            var dialogResult = XtraMessageBox.Show(text, @"Delete View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
-            if (dialogResult != DialogResult.Yes)
-            {
-                e.Cancel = true;
-            }
-            else
-            {
-                ResultsView.AssignViewForSearches(removedViewName, "Results", _ebaySearches.ChildrenCore);
-
-                for (int i = 0; i < treeList1.Nodes.Count; i++)
-                {
-                    if (treeList1.GetDataRecordByNode(treeList1.Nodes[i]) is Keyword2Find)
-                    {
-                        if (treeList1.Nodes[i].GetValue("View").ToString() == removedViewName)
-                        {
-                            treeList1.Nodes[i].SetValue("View", @"Results");
-                        }
-                    }
-                }
-
-                repositoryItemViews.Items.Remove(e.Item);
-                var mruEdit = (MRUEdit)sender;
-                if (mruEdit.Parent is TreeList treelist2)
-                    if (treelist2.GetDataRecordByNode(treelist2.FocusedNode) is Keyword2Find focusedKw2Find)
-                    {
-                        mruEdit.SelectedItem = focusedKw2Find.ViewName;
-                        mruEdit.EditValue = focusedKw2Find.ViewName;
-                        mruEdit.Text = focusedKw2Find.ViewName;
-                    }
-
-                var obsoletePanel = dockManager1.Panels.FirstOrDefault(p => p.Tag?.ToString() == removedViewName);
-                obsoletePanel?.Dispose();
-                ResultsView.RemoveFromViewList(removedViewName);
-                mruEdit.Properties.Items.Remove(e.Item);
-                treeList1.Refresh();
-            }
-        }
-
-        private static void RepositoryItemViews_SelectedIndexChanged(object sender, EventArgs e)
-        {
-            var mruEdit = (MRUEdit)sender;
-
-            if (mruEdit.SelectedIndex == -1)
-                return;
-
-            if (!(mruEdit.Parent is TreeList treeList))
-                return;
-
-            if (treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find)
-            {
-                var viewName = mruEdit.SelectedItem.ToString();
-                ResultsView.AssignViewToSearch(viewName, focusedKw2Find);
-            }
-        }
-
-        private void RepositoryItemViews_ButtonClick(object sender, ButtonPressedEventArgs e)
-        {
-            var mruEdit = (MRUEdit)sender;
-            if (e.Button.Caption == "Create View")
-                ShowAddViewDialog(mruEdit);
-
-            return;
-
-            if (e.Button.Caption == "Create View")
-            {
-                switch (mruEdit.Properties.TextEditStyle)
-                {
-                    case TextEditStyles.Standard: //Disable
-                        MruManager.DisableEdit(repositoryItemViews);
-
-                        break;
-                    case TextEditStyles.DisableTextEditor: //Enable
-                        MruManager.EnableEdit(repositoryItemViews);
-                        treeList1.ShowEditor();
-                        treeList1.ActiveEditor.Select();
-
-                        break;
-                }
-            }
-
-            if (e.Button.Caption == "Cancel") //Disable
-            {
-                treeList1.CancelCurrentEdit();
-                MruManager.DisableEdit(repositoryItemViews);
-            }
-        }
-
-        private void ShowAddViewDialog(MRUEdit mruEdit)
-        {
-            XtraInputBoxArgs args = new XtraInputBoxArgs();
-            // set required Input Box options
-            args.Caption = "Create View panel";
-            args.Prompt = "View name";
-            args.DefaultButtonIndex = 0;
-
-            // initialize a DateEdit editor with custom settings
-            var viewNameEdit = new TextEdit();
-            //viewNameEdit.Properties.CalendarView = DevExpress.XtraEditors.Repository.CalendarView.TouchUI;
-            //viewNameEdit.Properties.Mask.EditMask = "MMMM d, yyyy";
-            args.Editor = viewNameEdit;
-
-            // a default DateEdit value
-            if (!(treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find focusedKw2Find))
-                return;
-
-            args.DefaultResponse = focusedKw2Find.Alias;
-            // display an Input Box with the custom editor
-            var show = XtraInputBox.Show(args);
-            if (show != null)
-            {
-                var newViewName = show.ToString();
-                ResultsView.CreateView(newViewName);
-                GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
-                UserSettings.CanShowEbaySearchEditor = false;
-                ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);
-
-                MruManager.AddMissingMruItem(newViewName, repositoryItemViews);
-                //  mruEdit.Properties.Items.Add(newViewName);
-
-                foreach (TreeListNode treeList1Node in treeList1.Selection)
-                {
-                    treeList1.RefreshNode(treeList1Node);
-                }
-
-                //mruEdit.SelectedItem = newViewName;
-                //   mruEdit.SelectedIndex = 0;
-                //     mruEdit.EditValue = newViewName;
-            }
-
-            mruEdit.SelectedItem = focusedKw2Find.ViewName;
-            //MruManager.DisableEdit(repositoryItemViews);
-        }
-
-        private void repositoryItemCheckedComboBoxEditListingType_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
-        {
-            var term = e.Value as string;
-            if (string.IsNullOrEmpty(term))
-            {
-                e.DisplayText = "Buy It Now";
-            }
-            else
-            {
-                if (term.Contains(ListingType.BuyItNow.ToString()) && term.Contains(ListingType.AuctionsEndingNow.ToString()) && term.Contains(ListingType.AuctionsStartedNow.ToString()))
-                {
-                    e.DisplayText = "Any";
-                }
-            }
-        }
-
-        private void barEditItemAutoSelect_EditValueChanged(object sender, EventArgs e)
-        {
-            TopRowFocus.TopRowFocusInterval = double.Parse(barEditItemAutoSelect.EditValue.ToString(), CultureInfo.InvariantCulture);
-        }
-
-        private void btnHighlightWords_Click(object sender, EventArgs e)
-        {
-            var formHighlightWords = new FormHighlightWords();
-            formHighlightWords.ShowDialog(this);
-            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
-            SaveSettings();
-        }
-
-        private void btnBrowserSettings_Click(object sender, EventArgs e)
-        {
-            if (flyoutPanelBrowser.IsPopupOpen)
-                flyoutPanelBrowser.HidePopup();
-            else
-            {
-                flyoutPanelBrowser.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
-                flyoutPanelBrowser.Options.Location = new Point(-327, -5);
-                flyoutPanelBrowser.ShowPopup();
-            }
-        }
-
-        private void btnPicturesSettings_Click(object sender, EventArgs e)
-        {
-            //if (flyoutPanel1.IsPopupOpen)
-            //    flyoutPanel1.HidePopup();
-            //else
-            //{
-            //    flyoutPanel1.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
-            //    flyoutPanel1.Options.Location = new Point(-255, -5);
-            //    flyoutPanel1.ShowPopup();
-            //}
-        }
-
-        private void OpenTagLinkByButton(object sender, ItemClickEventArgs e)
-        {
-            var hyperLink = e.Item;
-            if (hyperLink.Tag != null)
-                Process.Start(hyperLink.Tag.ToString());
-        }
-
-        private async void repositoryItemTreeListLookUpEditCategory_BeforePopup(object sender, EventArgs e)
-        {
-            var treeList = ((TreeListLookUpEdit)sender).Properties.TreeList;
-
-            treeList.AfterCheckNode += TreeList_AfterCheckNode;
-            var siteName = "eBay US";
-
-            /*
-            CategoryLookup.AddColumns(repositoryItemTreeListLookUpEdit1TreeList);
-            var categoryService = new CategoryService(new ApiService(Creds.GetApiContextItem(SiteCodeType.US)));
-
-            repositoryItemTreeListLookUpEditCategory.KeyMember = "CategoryID";
-            repositoryItemTreeListLookUpEditCategory.ValueMember = "CategoryID";
-            repositoryItemTreeListLookUpEditCategory.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
-            repositoryItemTreeListLookUpEdit1TreeList.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
-            repositoryItemTreeListLookUpEditCategory.TreeList.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
-            //repositoryItemTreeListLookUpEditCategory.KeyMember = "CategoryID";
-            //repositoryItemTreeListLookUpEditCategory.ValueMember = "CategoryID";
-            //repositoryItemTreeListLookUpEditCategory.DisplayMember = "CategoryID";
-            //await CategoryLookup.InitTreelistLookup(repositoryItemTreeListLookUpEditCategory.TreeList, siteName);
-            */
-            await CategoryLookup.InitTreelistLookup(treeList, siteName);
-        }
-
-        private void TreeList_AfterCheckNode(object sender, NodeEventArgs e)
-        {
-            var categories = new List<string>();
-            var treeList = ((TreeList)sender);
-            if (e.Node.CheckState == CheckState.Checked)
-            {
-                foreach (var n in treeList.GetAllCheckedNodes())
-                {
-                    categories.Add(n.GetValue("CategoryID").ToString());
-                }
-            }
-
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
-            {
-                ebaySearch.Categories4Api = string.Join(",", categories);
-                if (treeList1.ActiveEditor != null)
-                    treeList1.ActiveEditor.EditValue = ebaySearch.Categories4Api;
-
-                //repositoryItemTreeListLookUpEditCategory.GetRowByKeyValue()
-            }
-
-            treeList1.Refresh();
-            treeList1.RefreshDataSource();
-        }
-
-        private void repositoryItemTreeListLookUpEditCategory_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
-        {
-            e.DisplayText = e.Value.ToString();
-
-            return;
-
-            Debug.WriteLine(e.Value);
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
-            {
-                e.DisplayText = ebaySearch.Categories4Api;
-            }
-
-            return;
-
-            //RepositoryItemTreeListLookUpEdit edit = sender as RepositoryItemTreeListLookUpEdit;
-            //object row = edit.TreeList..getrow.GetRowByKeyValue(e.Value);
-            //if (row != null)
-            //{
-            //    string text = string.Empty;
-            //    PropertyDescriptorCollection propertyDescriptors = ListBindingHelper.GetListItemProperties(edit.Properties.DataSource);
-            //    foreach (TreeListColumn column in edit.Properties.TreeList.Columns)
-            //    {
-            //        PropertyDescriptor pd = propertyDescriptors[column.FieldName];
-            //        object val = pd.GetValue(row);
-            //        if (val != null)
-            //            text += val.ToString() + ",";
-            //    }
-            //    e.DisplayText = e.Value.ToString();
-            //}
-        }
-
-        private async void repositoryItemPopupContainerEditCategory_Popup(object sender, EventArgs e)
-        {
-            popupContainerControlCategory.Size = new Size(300, 250);
-            treeListCategory.KeyFieldName = "CategoryID";
-            //treeListCategory.AfterCheckNode -= TreeListCategory_AfterCheckNode;
-            var ebaySite = "eBay US";
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch1)
-            {
-                ebaySite = ebaySearch1.EBaySite.SiteName;
-            }
-
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm1)
-            {
-                ebaySite = childTerm1.GetParent().EBaySite.SiteName;
-            }
-
-            await CategoryLookup
-                .InitTreelistLookup(treeListCategory, ebaySite)
-                .ContinueWith(t =>
-                {
-                    var categoriesList = new List<string>();
-                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
-                    {
-                        categoriesList = ebaySearch.Categories4Api.Split(',').ToList();
-                    }
-
-                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
-                    {
-                        categoriesList = childTerm.CategoryIDs.ToList();
-                    }
-
-                    treeListCategory.BeginUpdate();
-                    treeListCategory.UncheckAll();
-
-                    for (var i = 0; i < categoriesList.Count; i++)
-                    {
-                        if (i >= 3)
-                            break;
-
-                        var n = categoriesList[i];
-                        var nodeToCheck = treeListCategory.FindNodeByKeyID(n);
-                        SetParentsCheckState(nodeToCheck, CheckState.Indeterminate);
-                        if (nodeToCheck != null)
-                            treeListCategory.SetNodeCheckState(nodeToCheck, CheckState.Checked);
-                    }
-
-                    treeListCategory.EndUpdate();
-                }, TaskScheduler.FromCurrentSynchronizationContext());
-        }
-
-        private void SetParentsCheckState(TreeListNode nodeToCheck, CheckState checkState)
-        {
-            if (nodeToCheck != null && nodeToCheck.ParentNode != null)
-            {
-                nodeToCheck.ParentNode.CheckState = checkState;
-                SetParentsCheckState(nodeToCheck.ParentNode, checkState);
-            }
-        }
-
-        private void SetChildrenCheckState(TreeListNode nodeToCheck, CheckState checkState)
-        {
-            if (nodeToCheck.Nodes != null)
-                foreach (TreeListNode child in nodeToCheck.Nodes)
-                {
-                    child.CheckState = checkState;
-                    SetChildrenCheckState(child, checkState);
-                }
-        }
-
-        private void TreeListCategory_AfterCheckNode(object sender, NodeEventArgs e)
-        {
-            var treeList = (TreeList)sender;
-
-            var checkedNodes = treeList.GetAllCheckedNodes();
-
-            if (checkedNodes.Count > 3)
-            {
-                e.Node.CheckState = CheckState.Unchecked;
-                checkedNodes = treeList.GetAllCheckedNodes();
-                for (var i = 0; i < checkedNodes.Count; i++)
-                {
-                    if (i >= 3)
-                        checkedNodes[i].CheckState = CheckState.Unchecked;
-                }
-
-                XtraMessageBox.Show(treeList, "Sorry, maximum 3 categories allowed.");
-
-                return;
-            }
-
-            if (e.Node.CheckState == CheckState.Unchecked)
-            {
-                SetParentsCheckState(e.Node, CheckState.Unchecked);
-                SetChildrenCheckState(e.Node, CheckState.Unchecked);
-            }
-
-            var categoriesIds = new List<string>();
-            if (e.Node.CheckState == CheckState.Checked)
-            {
-                SetParentsCheckState(e.Node, CheckState.Indeterminate);
-                SetChildrenCheckState(e.Node, CheckState.Unchecked);
-            }
-
-            for (var i = 0; i < checkedNodes.Count; i++)
-            {
-                if (i >= 3)
-                    checkedNodes[i].CheckState = CheckState.Unchecked;
-                else
-                    categoriesIds.Add(checkedNodes[i].GetValue("CategoryID").ToString());
-            }
-
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
-            {
-                ebaySearch.Categories4Api = string.Join(",", categoriesIds);
-                if (treeList1.ActiveEditor != null)
-                {
-                    treeList1.RefreshNode(treeList1.FocusedNode);
-                    treeList1.ActiveEditor.EditValue = ebaySearch.Categories4Api;
-                }
-            }
-
-            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
-            {
-                childTerm.CategoryIDs = categoriesIds.ToArray();
-                if (treeList1.ActiveEditor != null)
-                {
-                    treeList1.RefreshNode(treeList1.FocusedNode);
-                    treeList1.ActiveEditor.EditValue = string.Join(",", childTerm.CategoryIDs);
-                }
-            }
-        }
-
-        private void barCheckPaypalPayment_CheckedChanged(object sender, ItemClickEventArgs e)
-        {
-            if (barEditItemProfile.EditValue == null || string.IsNullOrWhiteSpace(barEditItemProfile.EditValue.ToString()))
-            {
-                XtraMessageBox.Show("Please, select Chrome profile.");
-                return;
-            }
-
-            CreditCardService.CreditCardPaymentEnabled = ((BarCheckItem)sender).Checked;
-        }
-
-        private void barButtonItemShortcuts_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var formShortcuts = new FormShortcuts();
-            formShortcuts.ShowDialog();
-        }
-
-        private void BarCheckNoConfirmations_CheckedChanged(object sender, ItemClickEventArgs e)
-        {
-            UserSettings.SkipBuyConfirmation = ((BarCheckItem)sender).Checked;
-        }
-
-        private void PopupMenuOpenInBrowser_BeforePopup(object sender, CancelEventArgs e)
-        {
-            var menuItems = CreateOpenInBrowserMenuItems();
-            popupMenuOpenInBrowser.BeginUpdate();
-            popupMenuOpenInBrowser.ItemLinks.Clear();
-            popupMenuOpenInBrowser.ItemLinks.AddRange(menuItems);
-            popupMenuOpenInBrowser.EndUpdate();
-        }
-
-        private void BtnSortFilters_Click(object sender, EventArgs e)
-        {
-            lstchkXfilterList.BeginUpdate();
-            XFilterManager.SortFilters();
-            lstchkXfilterList.EndUpdate();
-        }
-
-        private void barButtonItemTelegram_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var formTelegram = new FormTelegram();
-
-            if (_telegramSender == null)
-                _telegramSender = new TelegramSender();
-            else if (_telegramSender.IsListening())
-                _telegramSender.StopListening();
-            var tmp = new TelegramSender();
-
-            tmp.TelegramAccount = _telegramSender.TelegramAccount;
-            tmp.TelegramBotID = _telegramSender.TelegramBotID;
-            tmp.CombinePushes = _telegramSender.CombinePushes;
-            tmp.BodyColumns = _telegramSender.BodyColumns;
-            tmp.BodyTemplate = _telegramSender.BodyTemplate;
-
-            tmp.Enabled = _telegramSender.Enabled;
-            tmp.SetMaxMessagesPerMinute(_telegramSender.MaxMessagesPerMinute);
-            tmp.PicturesCountToAttach = _telegramSender.PicturesCountToAttach;
-            tmp.TelegramChatID = _telegramSender.TelegramChatID;
-
-            if (!string.IsNullOrEmpty(tmp.TelegramBotID))
-            {
-                try
-                {
-                    tmp.CreateTelegramBotClient(tmp.TelegramBotID);
-                }
-                catch (Exception exception)
-                {
-                    XtraMessageBox.Show(exception.Message);
-                }
-            }
-
-            formTelegram.TelegramTmp = tmp;
-            formTelegram.ebaySearches = _ebaySearches.ChildrenCore;
-            var result = formTelegram.ShowDialog();
-            formTelegram.TelegramTmp.StopListening();
-            if (result == DialogResult.OK)
-            {
-                _telegramSender = formTelegram.TelegramTmp;
-                SaveSettings();
-            }
-
-            if (!_telegramSender.IsListening())
-            {
-                _telegramSender.StartListening();
-            }
-        }
-
-        private void OpenTagLink(object sender, EventArgs e)
-        {
-            Process.Start(((LabelControl)sender).Tag.ToString());
-        }
-
-        private void repositoryItemPopupContainerEditFilter_QueryResultValue(object sender, QueryResultValueEventArgs e)
-        {
-            e.Value = filterControlTerm.FilterCriteria;
-        }
-
-        internal void ToggleCheckoutPermission()
-        {
-            if (ConnectionConfig.CheckoutEnabled || ConnectionConfig.SkipBuyConfirmation)
-            {
-                ribbonPageCheckout.Visible = true;
-            }
-            else
-            {
-                ribbonPageCheckout.Visible = false;
-            }
-
-            if (ConnectionConfig.CheckoutEnabled)
-            {
-                barCheckImmediatePayment.Visibility = BarItemVisibility.Always;
-                barEditItemProfile.Visibility = BarItemVisibility.Always;
-                ribbonPageGroupProfile.Visible = true;
-            }
-            else
-            {
-                barCheckImmediatePayment.Checked = false;
-                barCheckImmediatePayment.Visibility = BarItemVisibility.Never;
-                barEditItemProfile.Visibility = BarItemVisibility.Never;
-                ribbonPageGroupProfile.Visible = false;
-            }
-
-            if (ConnectionConfig.SkipBuyConfirmation)
-            {
-                barCheckNoConfirmations.Visibility = BarItemVisibility.Always;
-            }
-            else
-            {
-                barCheckNoConfirmations.Checked = false;
-                barCheckNoConfirmations.Visibility = BarItemVisibility.Never;
-            }
-        }
-
-        private void barEditItemProfile_EditValueChanged(object sender, EventArgs e)
-        {
-            CookieManager.Profile = (CookieProfile)((BarEditItem)sender).EditValue;
-        }
-
-        private void barStaticLicense_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            ShowActivationWindow();
-        }
-
-        private void barEditItemInitialResultsLimit_EditValueChanged(object sender, EventArgs e)
-        {
-            if (int.TryParse(((BarEditItem)sender).EditValue.ToString(), out var initialResultsLimit))
-            {
-                UserSettings.InitialResultsLimit = initialResultsLimit;
-            }
-        }
-
-        private void workspaceManager1_WorkspaceSaved(object sender, WorkspaceEventArgs args)
-        {
-            if (args.Workspace.Name is "Workspace Active" or "Basic Layout (Default)" or "Full Details (Default)" or "DefaultWorkspace")
-                return;
-            //Analytics.AddEvent("", "WorkspaceSaved", 1);
-        }
-
-        private void barButtonItemIgnoreSellers_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var authForm = new FormIgnoreSellers();
-            var result = authForm.ShowDialog();
-            if (result == DialogResult.OK)
-            {
-                SaveSettings();
-            }
-        }
-
-        private void repositoryItemCheckEditEnabled_EditValueChanged(object sender, EventArgs e)
-        {
-            var treeList = (sender as BaseEdit).Parent as TreeList;
-            var node = treeList.FocusedNode;
-            var isChecked = (bool)(sender as CheckEdit).EditValue;
-            node.Checked = isChecked;
-        }
-
-        private async void barButtonItemRestoreBackup_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            using var openFileDialog = new OpenFileDialog();
-            openFileDialog.Filter = "Backup/config files (*.cfg*, *.zip)|*.cfg*;*.zip";
-            openFileDialog.FilterIndex = 1;
-            openFileDialog.RestoreDirectory = true;
-            openFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
-            openFileDialog.Title = "Restore Backup from ZIP file or Config file";
-            if (openFileDialog.ShowDialog() == DialogResult.OK)
-            {
-                if (_searchService is { Running: true })
-                    await StopWorking();
-
-                var backupFilePath = openFileDialog.FileName;
-                var fileExtension = Path.GetExtension(backupFilePath).ToLower();
-
-                if (fileExtension == ".zip")
-                {
-                    // Extract ZIP file
-                    var tempExtractPath = Path.Combine(Path.GetTempPath(), "uBuyFirst_Backup");
-                    if (Directory.Exists(tempExtractPath))
-                        Directory.Delete(tempExtractPath, true);
-
-                    Directory.CreateDirectory(tempExtractPath);
-                    ZipFile.ExtractToDirectory(backupFilePath, tempExtractPath);
-
-                    // Load settings from extracted files
-                    var configFilePath = Path.Combine(tempExtractPath, "config.cfg");
-                    if (File.Exists(configFilePath))
-                        LoadSettings(configFilePath);
-
-                    var workSpaceFilePath = Path.Combine(tempExtractPath, "Workspace Active.xml");
-                    if (File.Exists(workSpaceFilePath))
-                    {
-                        File.Copy(workSpaceFilePath, Path.Combine(Folders.Workspace, "Workspace Active.xml"), true);
-                        ApplyWorkSpaceOnFormShown();
-                    }
-
-                    var layoutFilePath = Path.Combine(tempExtractPath, "Layout Bid Window.xml");
-                    if (File.Exists(layoutFilePath))
-                        File.Copy(layoutFilePath, Path.Combine(Folders.Layout, "Layout Bid Window.xml"), true);
-                    ClearOldResults();
-                    // Add additional restore logic here for other files if needed
-                    MessageBox.Show("Backup restored from ZIP file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);
-
-                    // Cleanup the temporary extracted files
-                    Directory.Delete(tempExtractPath, true);
-                }
-                else
-                {
-                    // Load the settings from the .cfg file
-                    LoadSettings(backupFilePath);
-                    ClearOldResults();
-                    MessageBox.Show("Backup restored from CFG file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);
-                }
-            }
-        }
-
-
-        private void barButtonItemCreateBackup_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            // Create a SaveFileDialog to prompt the user where to save the ZIP file
-            using var saveFileDialog = new SaveFileDialog();
-            saveFileDialog.Filter = "ZIP Files (*.zip)|*.zip";
-            saveFileDialog.Title = "Save Backup";
-            saveFileDialog.FileName = $"uBuyFirst Backup {DateTime.Now.ToString("yyyy-MM-dd")}.zip";
-            saveFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
-            // Show the dialog and check if the user clicked OK
-            if (saveFileDialog.ShowDialog() == DialogResult.OK)
-            {
-                // Get the destination path where the ZIP file will be saved
-                var zipFilePath = saveFileDialog.FileName;
-                if (File.Exists(zipFilePath))
-                {
-                    // Delete the existing file
-                    File.Delete(zipFilePath);
-                }
-                BackupToZipFile(zipFilePath);
-
-                MessageBox.Show("Backup created successfully!", "Backup", MessageBoxButtons.OK, MessageBoxIcon.Information);
-            }
-        }
-
-        private static void BackupToZipFile(string zipFilePath)
-        {
-            // Create the ZIP file and add the necessary files
-            using (var zipArchive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
-            {
-                AddFileToZip(zipArchive, Folders.Settings, "config.cfg");
-                AddFileToZip(zipArchive, Folders.Workspace, "Workspace Active.xml");
-                AddFileToZip(zipArchive, Folders.Layout, "Layout Bid Window.xml");
-            }
-        }
-
-        // Helper method to add files to the ZIP archive if they exist
-        private static void AddFileToZip(ZipArchive zipArchive, string folderPath, string fileName)
-        {
-            var sourceFilePath = Path.Combine(folderPath, fileName);
-            if (File.Exists(sourceFilePath))
-            {
-                zipArchive.CreateEntryFromFile(sourceFilePath, fileName);
-            }
-        }
-        public static void ShowTrayBalloon(string title, string text, int timeout)
-        {
-            if (Form1.Instance.notifyIcon1 != null && Form1.Instance.IsHandleCreated && !Form1.Instance.IsDisposed)
-                Form1.Instance.InvokeIfRequired(() =>
-                {
-                    Form1.Instance.notifyIcon1.BalloonTipTitle = title;
-                    Form1.Instance.notifyIcon1.BalloonTipText = text;
-                    Form1.Instance.notifyIcon1.ShowBalloonTip(timeout * 1000);
-                });
-        }
-
-        private void barCheckItemPriceOpensCheckout_CheckedChanged(object sender, ItemClickEventArgs e)
-        {
-            UserSettings.ClickOnPriceOpensProductPage = !((BarCheckItem)sender).Checked;
-        }
-
-        private void barButtonItemSync_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var formSync = new FormSyncSearchTerms();
-            _searchTermSyncTimer.Stop();
-            formSync.ShowDialog();
-            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
-            _searchTermSyncTimer.Start();
-            SaveSettings();
-        }
-
-        private void barButtonItemGetExternalData_ItemClick(object sender, ItemClickEventArgs e)
-        {
-            var externalDataForm = new FormExternalData(_cefBrowserManager);
-            externalDataForm._eBaySearches = _ebaySearches.ChildrenCore;
-            externalDataForm.ShowDialog();
-        }
-
-        private void galleryControl1_Click(object sender, EventArgs e)
-        {
-
-        }
-
-        private void pictureSettingsButton_Click(object sender, EventArgs e)
-        {
-            lock (_chkLargeImagesOnHoverValueChangedLock)
-            {
-                panelPicturesSettingControl.Visible = !panelPicturesSettingControl.Visible;
-            }
-        }
-
-        private void zoomTrackBarExpanded_EditValueChanged(object sender, EventArgs e)
-        {
-        }
-
-    }
-}
+using System;
+using System.Collections.Generic;
+using System.ComponentModel;
+using System.Diagnostics;
+using System.Drawing;
+using System.Globalization;
+using System.IO;
+using System.IO.Compression;
+using System.Linq;
+using System.Media;
+using System.Net;
+using System.Reflection;
+using System.Text.RegularExpressions;
+using System.Threading;
+using System.Threading.Tasks;
+using System.Windows.Forms;
+using DevExpress.LookAndFeel;
+using DevExpress.Skins;
+using DevExpress.Utils;
+using DevExpress.XtraBars;
+using DevExpress.XtraBars.Docking;
+using DevExpress.XtraBars.Docking2010;
+using DevExpress.XtraBars.Ribbon;
+using DevExpress.XtraBars.Ribbon.Gallery;
+using DevExpress.XtraEditors;
+using DevExpress.XtraEditors.Controls;
+using DevExpress.XtraGrid.Views.BandedGrid;
+using DevExpress.XtraGrid.Views.Base;
+using DevExpress.XtraGrid.Views.Grid;
+using DevExpress.XtraGrid.Views.Grid.ViewInfo;
+using DevExpress.XtraLayout;
+using DevExpress.XtraTreeList;
+using DevExpress.XtraTreeList.Nodes;
+using eBay.Service.Core.Sdk;
+using eBay.Service.Core.Soap;
+using ExceptionReporting;
+using NLog;
+using uBuyFirst.AI;
+using uBuyFirst.Auth;
+using uBuyFirst.BrowseAPI;
+using uBuyFirst.CefBrowser;
+using uBuyFirst.CustomClasses;
+using uBuyFirst.Data;
+using uBuyFirst.Filters;
+using uBuyFirst.Grid;
+using uBuyFirst.GUI;
+using uBuyFirst.Images;
+using uBuyFirst.Item;
+using uBuyFirst.License;
+using uBuyFirst.MQTT;
+using uBuyFirst.Network;
+using uBuyFirst.Other;
+using uBuyFirst.Prefs;
+using uBuyFirst.Properties;
+using uBuyFirst.Purchasing;
+using uBuyFirst.Purchasing.Cookies;
+using uBuyFirst.Search;
+using uBuyFirst.Search.Status;
+using uBuyFirst.Security;
+using uBuyFirst.Stats;
+using uBuyFirst.SubSearch;
+using uBuyFirst.Telegram;
+using uBuyFirst.Time;
+using uBuyFirst.Tools;
+using uBuyFirst.Update;
+using uBuyFirst.Views;
+using SiteCodeType = eBay.Service.Core.Soap.SiteCodeType;
+
+[assembly:
+    Obfuscation(Exclude = false,
+        Feature = "preset(minimum);"
+                  + "+anti debug;"
+                  + "+anti dump;"
+                  + "+anti ildasm;"
+                  + "+anti tamper(key=dynamic);"
+                  + "+constants;"
+                  + "+ctrl flow;"
+                  + "+invalid metadata;"
+                  + "+ref proxy;"
+                  + "-rename;")]
+[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]
+
+namespace uBuyFirst
+{
+    public partial class Form1
+    {
+        //Modules - Alert, Tracking, Logging, eBay Searches, eBay Accounts, PlaceOffer, Filtering, Pushbullet, Specifics, Browser, Licensing, Settings, Updater
+
+        //tracking
+
+        //lists
+        public static BindingList<EbayAccount> EBayAccountsList;
+        internal QueryList _ebaySearches;
+
+        //Utils
+        private Updater _updater;
+        private readonly GuiChanger _guiChanger;
+        private FocusRouter _focusRouter;
+        private PushbulletSender _pushbullet;
+        private TelegramSender _telegramSender;
+        private SoundPlayer _myPlayer;
+        private readonly AblyClient _ablyClient;
+        private readonly MQTTManager _mqttManager;
+        public static readonly Logger Log = LogManager.GetLogger("Logger");
+        public static LicenseUtility LicenseUtility;
+        public static SecurePhpConnection Secure = new SecurePhpConnection();
+        public FilterAdapter FilterAdapter1;
+
+        public SearchService _searchService;
+
+        //time
+        private System.Windows.Forms.Timer _1SecTimer;
+        private System.Windows.Forms.Timer _1HourTimer;
+        private System.Windows.Forms.Timer _searchTermSyncTimer;
+
+        //Cancellation
+        internal CancellationToken CancelTokenOnRowChange;
+        internal CancellationTokenSource CancelTokenSourceOnRowChange = new CancellationTokenSource();
+
+        //Forms
+        public static Form1 Instance { get; private set; }
+
+        private FormReportDialog _formReportDialog;
+
+        public static SynchronizationContext _synchronizationContext;
+        private static Helpers.AdvancedSearchConfig _requestsCfg;
+        private Point _filterItemLocationPoint = Point.Empty;
+
+        public static bool _showHighBidder;
+        private static GetItemsStatus _checkItemsStatus;
+
+        private static SemaphoreSlim _getItemQueueLimiter;
+
+        private TrayManager _trayManager;
+        private ViewReporter _viewReporter;
+        private PicManager _picManager;
+
+        public static bool EBayAccountInitCompleted { get; set; }
+        private static bool _saveActiveWorkspace = true;
+        public static BuyingService BuyingService;
+        public static Analytics? GoogleAnalytics;
+        private readonly CefBrowserManager _cefBrowserManager;
+
+        private object _chkLargeImagesOnHoverValueChangedLock = new object();
+        public static int LastSliderValueSmall = 100;
+        public static int LastSliderValueLarge = 100;
+
+        private void InitVars()
+        {
+            BaseGallery.AllowHoverAnimation = false;
+            _synchronizationContext = SynchronizationContext.Current;
+            ExM.ExceptionsList = new List<string>();
+            EBayAccountsList = new BindingList<EbayAccount>();
+            EBayAccountsList.ListChanged += EbayAccount.EBayAccountsList_ListChanged;
+            ProgramState.Isdebug = Debugger.IsAttached;
+            ProgramState.Idlesw = new Stopwatch();
+            ProgramState.LastSelectedItemID = "";
+            ProgramState.TotalRunningStopwatch = new Stopwatch();
+            ProgramState.SearchingStopwatchGA4 = new Stopwatch();
+            ExM.Reporter = new ExceptionReporter();
+            ExM.SynchronizationContextCurrent = SynchronizationContext.Current;
+            Stat.TotalItemsProcessed = 0;
+            _myPlayer = new SoundPlayer { Stream = Resources.binbloop };
+
+            ProgramState.PublicIp = "************";
+
+            _focusRouter = new FocusRouter(webBrowser1);
+
+            ResultsView.DockManager = dockManager1;
+            ResultsView.RepositoryItemViews = repositoryItemViews;
+            FilterAdapter1 = new FilterAdapter();
+            _trayManager = new TrayManager(this);
+
+            TopRowFocus.FocusTimer.SynchronizingObject = Instance;
+            TopRowFocus.FocusTimer.AutoReset = false;
+            TopRowFocus.FocusTimer.Elapsed += FocusTimer_Elapsed;
+            TopRowFocus.FocusTimer.SynchronizingObject = this;
+
+            _picManager = new PicManager(galleryControl1);
+
+            //CategoryService = new CategoryService(ap);
+        }
+
+        public Form1()
+        {
+            InitializeComponent();
+            try
+            {
+                ProgramState.UBFVersion = ProductVersion;
+                Instance = this;
+                webBrowser1.PreviewKeyDown += FocusRouter.WebBrowserKeyDown;
+
+                MakeRunButtonStart();
+                dockManager1.BeginUpdate();
+                InitVars();
+                NetTools.InitHttPprotocol();
+
+                //defer
+                Loggers.SetupCloudLogging();
+                Upgrader.TryUpgrade();
+
+                ProgramState.HWID = LicenseUtility.GetHWID();
+                _ablyClient = new AblyClient(_ => { });
+                _mqttManager = new MQTTManager(PostMQQTItemToResultsGrid);
+                Log.Info("Started version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ";" + Environment.OSVersion);
+
+                //if (!ProgramState.Isdebug)
+                {
+                    Program.DeregisterExceptionHandlers();
+                    AppDomain.CurrentDomain.UnhandledException += ExM.ubuyExceptionHandler;
+                    Application.ThreadException += ExM.ubuyExceptionHandler;
+                    TaskScheduler.UnobservedTaskException += ExM.ubuyExceptionTaskHandler;
+                }
+
+                Folders.SetupFolders();
+
+                ItemSpecifics.CategorySpecificsList = new CustomBindingList<CategorySpecific>();
+                ItemSpecifics.CategorySpecificsList.ListChanged += ItemSpecifics.CategorySpecificsList_ListChanged;
+                ItemSpecifics.CategorySpecificsList.ItemDeleting += ItemSpecifics.CategorySpecificsList_ItemDeleting;
+
+                AiAnalysis.AiColumnsList = new CustomBindingList<string>() {
+                    "ai "+"Expiration Date",
+                    "ai "+"Expired",
+                    "ai "+"Box count",
+                    "ai "+"Opened box",
+                    "ai "+"Damaged packaging",
+                    "ai "+"Missing items",
+                    "ai "+"Visual evaluation",
+                    "ai "+"Reasoning"}; 
+
+                AiAnalysis.AiColumnsList.ListChanged += AiAnalysis.AiColumnsList_ListChanged;
+                AiAnalysis.AiColumnsList.ItemDeleting += AiAnalysis.AiColumnsList_ItemDeleting;
+                SetupGUI();
+
+                var configPath = Path.Combine(Folders.Settings, "config.cfg");
+                LoadSettings(configPath);
+
+                WebView.DisableClickSounds();
+
+                Helpers.CheckDotNetVersion();
+
+                _cefBrowserManager = new CefBrowserManager();
+                var filesMissing = _cefBrowserManager.AreRequiredFilesMissing();
+                if (!filesMissing)
+                {
+                    _cefBrowserManager.InitializeCefEngine();
+                }
+
+                _guiChanger = new GuiChanger(Instance, _cefBrowserManager, panelCefBrowser);
+                GridViewEvents.GuiChanger = _guiChanger;
+                repositoryItemComboBoxSite.Items.AddRange(Intl.CountryProvider.GetEbaySiteNameList());
+                repositoryItemComboBoxLocatedIn.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeListWithAny());
+                repositoryItemComboBoxShipsTo.Items.AddRange(Intl.CountryProvider.GenerateCountryCodeList());
+                BuyingService = new BuyingService();
+            }
+            catch (Exception ex)
+            {
+                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
+                Application.Exit();
+            }
+
+            var geminiService = new GeminiService();
+            geminiService.SendHelloMessage();
+        }
+
+
+
+        public Form1(bool bIsFromTrialPrompt)
+        {
+            InitializeComponent();
+            try
+            {
+                if (bIsFromTrialPrompt)
+                {
+                    Folders.SetupFolders();
+                }
+            }
+            catch (Exception ex)
+            {
+                XtraMessageBox.Show($"Form1: {ex.Message} {ex.StackTrace} {ex.TargetSite}");
+                Application.Exit();
+            }
+        }
+
+        private void Form1_Load(object sender, EventArgs e)
+        {
+            Debug.WriteLine(DateTime.Now.ToString("T") + "**Loading");
+            dockManager1.LayoutVersion = "d3";
+            dockManager1.ForceInitialize();
+            documentManager1.ForceInitialize();
+
+            repositoryItemViews.SelectedIndexChanged += RepositoryItemViews_SelectedIndexChanged;
+            repositoryItemViews.ButtonClick += RepositoryItemViews_ButtonClick;
+
+            UserLookAndFeel.Default.ActiveLookAndFeel.StyleChanged += ActiveLookAndFeel_StyleChanged;
+            ChangePanelHeader();
+            SetTreeListCheckboxesState();
+        }
+
+        private void Form1_Shown(object sender, EventArgs e)
+        {
+            Debug.WriteLine(DateTime.Now.ToString("T") + "**Showing");
+            LoadWorkspacesOnFormLoad();
+            ApplyWorkSpaceOnFormShown();
+            var obsoletePanel = dockManager1.Panels.ToList().FirstOrDefault(p => p.ID == new Guid("92581858-ecdc-4d88-be24-9bb7948442dc"));
+            obsoletePanel?.Dispose();
+            ResultsView.TogglePanelsVisibility();
+            dockManager1.EndUpdate();
+
+            _1SecTimer = new System.Windows.Forms.Timer();
+            _1SecTimer.Tick += _1secTimer_Elapsed;
+            _1SecTimer.Interval = (int)TimeSpan.FromSeconds(1).TotalMilliseconds;
+            _1SecTimer.Tag = 0;
+            _1SecTimer.Start();
+
+            #region Licensing
+
+            LicenseUtility = new LicenseUtility();
+            LicenseUtility.SetDefaultLimits();
+
+            InitUpdater();
+            ImageCleaner.StartCleanUpTimer();
+            //after license completed
+            var fromCurrentSynchronizationContext = TaskScheduler.FromCurrentSynchronizationContext();
+            LicenseUtility
+                .CheckLicense()
+                .ContinueWith(_ =>
+                    ConnectionConfig.SetApiConfig()
+                    .ContinueWith(_ =>
+                    {
+                        barStaticLicense.Caption = LicenseUtility.GetLicenseStatusText();
+                        Loggers.InitGoogleAnalyticsV4(ProgramState.SerialNumberShort, LicenseUtility.CurrentSubscriptionType);
+
+                        ServicePointManager.ServerCertificateValidationCallback = Helpers.BuyItNowConfirmation;
+                        if (!ConnectionConfig.TradingAPIEnabled)
+                        {
+                            barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
+                        }
+
+                        ToggleCheckoutPermission();
+
+                        CookieManager.ReadChromeVersion();
+                        var profileListLoaded = AssignProfileCombobox();
+                        if (ConnectionConfig.CheckoutEnabled && profileListLoaded)
+                        {
+                            barCheckImmediatePayment.Checked = CreditCardService.CreditCardPaymentEnabled;
+                        }
+                        else
+                        {
+                            barCheckImmediatePayment.Checked = false;
+                            CreditCardService.CreditCardPaymentEnabled = false;
+                        }
+
+                        if (ConnectionConfig.SkipBuyConfirmation)
+                        {
+                            barCheckNoConfirmations.Checked = UserSettings.SkipBuyConfirmation;
+                        }
+                        else
+                        {
+                            barCheckNoConfirmations.Checked = false;
+                            UserSettings.SkipBuyConfirmation = false;
+                        }
+
+                        LicenseUtility.LicenseCheckCompleted = true;
+                    }, fromCurrentSynchronizationContext));
+
+            #endregion Licensing
+
+            InitBGtasks();
+            _viewReporter = new ViewReporter(this);
+
+            Log.Info("Loaded version: {0}", ProgramState.UBFVersion + "; " + ProgramState.HWID + ProgramState.SerialNumber);
+            //if (!webBrowser1.IsDisposed)webBrowser1.Navigate($"https://ubuyfirst.com/uBuyFirst/description.html?ver={ProgramState.UBFVersion}&license={ProgramState.SerialNumber}&hwid={ProgramState.HWID}&cb={new Random().Next(10000)}");
+
+            barCheckItemSoundAlert.CheckedChanged += barCheckItemSoundAlert_CheckedChanged;
+            galleryControl1.MouseLeave += _focusRouter.FocusBrowser;
+            lstchkXfilterList.ItemCheck += lstchkXfilterList_ItemCheck;
+
+            barStaticErrorsVal.Visibility = BarItemVisibility.Always;
+            if (Program.Sandbox)
+            {
+                Text = "Sandbox mode";
+                linkeBayPrivacyPolicy.Visible = true;
+                linkeBayUserAgreement.Visible = true;
+                linkReportItem.Visible = true;
+                picBoxEbayLogo.Visible = true;
+            }
+
+            if (Config.SafeEnabled)
+            {
+                linkeBayPrivacyPolicy.Visible = true;
+                linkeBayUserAgreement.Visible = true;
+                linkReportItem.Visible = true;
+                picBoxEbayLogo.Visible = true;
+            }
+
+            InitEbayAccounts();
+
+            _searchService = new SearchService(_requestsCfg, LicenseUtility, _viewReporter);
+
+            if (barCheckItemAutoStartSearch.Checked || Upgrader.ResumeSearchAfterUpgrade)
+            {
+                barButtonStart.PerformClick();
+            }
+
+            _synchronizationContext.Send(state => SaveSettings(), null);
+            int newHeight = Convert.ToInt32(16 * DpiProvider.Default.DpiScaleFactor + 2);
+            if (treeList1.RowHeight > 0 && treeList1.RowHeight < newHeight)
+            {
+                treeList1.RowHeight = newHeight;
+            }
+
+            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
+
+            _1HourTimer = new System.Windows.Forms.Timer();
+            _1HourTimer.Tick += _1HourTimer_Elapsed;
+            var timerInterval = 55;
+            _1HourTimer.Interval = (int)TimeSpan.FromMinutes(timerInterval).TotalMilliseconds;
+            _1HourTimer.Tag = 0;
+            _1HourTimer.Start();
+
+            _searchTermSyncTimer = new System.Windows.Forms.Timer();
+
+            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
+            _searchTermSyncTimer.Tick += _SearchTermSyncTimer_Elapsed;
+
+            Debug.WriteLine(DateTime.Now.ToString("T") + "**Shown");
+
+            if (Debugger.IsAttached || ProgramState.SerialNumber.StartsWith("ROMA"))
+                Program.AffiliateOff = true;
+            if (Program.FirstVisit)
+            {
+                //Analytics.AddEvent("", "first_visit", 1);
+                //Analytics.PostReport();
+            }
+        }
+
+        private bool AssignProfileCombobox()
+        {
+            var profileNames = CookieManager.GetProfileNamesFirefox();
+
+            // Clear existing items and add new ones.
+            repositoryItemComboBoxProfile.Items.Clear();
+            repositoryItemComboBoxProfile.Items.AddRange(profileNames);
+
+            // Check if there are any items in the ComboBox.
+            if (repositoryItemComboBoxProfile.Items.Count == 0)
+            {
+                return false;
+            }
+
+            // Determine which item to select in the ComboBox.
+            CookieProfile selectedItem;
+
+            if (repositoryItemComboBoxProfile.Items.Count == 1)
+            {
+                // If there is only one item in the ComboBox, select that item.
+                selectedItem = repositoryItemComboBoxProfile.Items[0] as CookieProfile;
+            }
+            else
+            {
+                // If there are multiple items, find the first one that matches the current profile.
+                var currentProfile = CookieManager.Profile;
+                selectedItem = repositoryItemComboBoxProfile.Items.OfType<CookieProfile>().FirstOrDefault(item => item.Profile == currentProfile?.Profile);
+            }
+
+            // If a matching profile is found or there's only one profile, set it as the edit value.
+            if (selectedItem != null)
+            {
+                barEditItemProfile.EditValue = selectedItem;
+                return true;
+            }
+
+            // Fallback: Set the first item as the edit value if no specific match is found.
+            barEditItemProfile.EditValue = repositoryItemComboBoxProfile.Items[0];
+            return true;
+        }
+
+        internal void ReporterDotsProgressChanged(object sender, byte e)
+        {
+            ShowDebugStats();
+            RunningDots();
+        }
+
+        internal void _reporterItem_ProgressChanged(object sender, FoundItem e)
+        {
+            HandleNewItem(e);
+        }
+
+        internal void ReportLogTxt(object sender, string e)
+        {
+            memoEditLog.Text += $"\r\n{e}";
+        }
+
+        private void BrowserDocCompleted(object sender, WebBrowserDocumentCompletedEventArgs e)
+        {
+            var webBrowser = (WebBrowser)sender;
+            if (webBrowser.DocumentText.Length > 0)
+            {
+                try
+                {
+                    WebView.SetZoom(webBrowser, UserSettings.BrowserFontSize);
+                }
+                catch (Exception ex)
+                {
+                    if (ProgramState.Isdebug)
+                        MessageBox.Show(ex.Message);
+                }
+            }
+        }
+
+        private void MouseMoveResetIdle(object sender, MouseEventArgs e)
+        {
+            ProgramState.Idlesw.Restart();
+        }
+
+        private void Dgv1KeyDown(object sender, KeyEventArgs e)
+        {
+            var grView = (AdvBandedGridView)sender;
+            try
+            {
+                if (e.KeyCode == Keys.Delete)
+                {
+                    if (grView.RowCount == 0)
+                    {
+                        _guiChanger.ClearItemInfoOnZeroRows();
+                    }
+
+                    e.Handled = true;
+                }
+            }
+            catch (Exception ex)
+            {
+                ExM.ubuyExceptionHandler("dgv1KeyDown: ", ex);
+            }
+        }
+
+        [Obfuscation(Exclude = false,
+            Feature = "preset(maximum);"
+                      + "+anti debug;"
+                      + "+anti dump;"
+                      + "+anti ildasm;"
+                      + "+anti tamper(key=dynamic);"
+                      + "+constants;"
+                      + "+ctrl flow;"
+                      + "+invalid metadata;"
+                      + "+ref proxy;"
+                      + "-rename;")]
+
+        #region eBay Accounts
+
+        private static async Task IsTokenValid(EbayAccount account)
+        {
+            try
+            {
+                if (string.IsNullOrEmpty(account?.TokenPo) || Program.Sandbox)
+                    return;
+
+                var apiContextPlaceOffer = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, account.TokenPo);
+
+                var tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
+                if (tokenStatus == null)
+                {
+                    tokenStatus = await Authenticator.GetTokenStatusAsync(apiContextPlaceOffer);
+                }
+
+                if (tokenStatus?.Status != TokenStatusCodeType.Active)
+                {
+                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
+                    EBayAccountsList.Remove(account);
+                }
+            }
+            catch (ApiException ex)
+            {
+                if (string.IsNullOrEmpty(account?.UserName))
+                    XtraMessageBox.Show(En_US.Form1_isTokenValid_eBay_account_was_removed_from_accounts_list);
+                else
+                    XtraMessageBox.Show($"Account: {account.UserName}\nRemoved from account list\nToken status: {ex.Message}");
+
+                EBayAccountsList.Remove(account);
+            }
+
+            catch (Exception ex)
+            {
+                ExM.ubuyExceptionHandler("isTokenValid: ", ex);
+            }
+        }
+
+        private void AddEbayAccount()
+        {
+            if (EBayAccountsList.Count > 0 && !LicenseUtility.CurrentLimits.MultipleEbayAccountsEnabled)
+            {
+                XtraMessageBox.Show(En_US.Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_);
+
+                return;
+            }
+
+            var account = ShowAuthorizeOnEbay();
+            if (account != null)
+            {
+                var isNewAccount = true;
+                for (var i = 0; i < EBayAccountsList.Count; i++)
+                {
+                    if (EBayAccountsList[i].UserName == account.UserName)
+                    {
+                        EBayAccountsList[i] = account;
+                        isNewAccount = false;
+
+                        break;
+                    }
+                }
+
+                if (isNewAccount)
+                {
+                    EBayAccountsList.Add(account);
+                    Analytics.AddEvent("", "EbayAccountAdded", 1);
+                }
+            }
+
+            SaveSettings();
+        }
+
+        public static EbayAccount ShowAuthorizeOnEbay(EbayAccount editAccount = null)
+        {
+            //AutoMeasurement.Client.TrackScreenView("Screen - Authorize on ebay");
+
+            var authenticator = new Authenticator(editAccount);
+            var authForm = new FormAuth(editAccount, authenticator);
+
+            var result = authForm.ShowDialog();
+            if (result == DialogResult.OK)
+            {
+                if (!string.IsNullOrEmpty(authForm.EbayAccount.RefreshToken))
+                {
+                    return authForm.EbayAccount;
+                }
+
+                if (string.IsNullOrEmpty(authForm.EbayAccount.UserName) || string.IsNullOrEmpty(authForm.EbayAccount.TokenPo))
+                {
+                    return null;
+                }
+
+                return authForm.EbayAccount;
+            }
+
+            return null;
+        }
+
+        #endregion eBay Accounts
+
+        private bool isShutdownInitiated = false;
+
+        private async void ClosingMainForm(object sender, FormClosingEventArgs e)
+        {
+            // Check if shutdown has already been initiated
+            if (isShutdownInitiated)
+            {
+                // Shutdown is already in progress, so do nothing
+                return;
+            }
+
+            // Indicate that shutdown process has been initiated
+            isShutdownInitiated = true;
+
+            // Prevent the form from closing immediately
+            e.Cancel = true;
+            Cursor.Current = Cursors.WaitCursor;
+            Text = "uBuyFirst - Shutting down";
+
+            try
+            {
+                // Disconnect from services asynchronously
+                _ablyClient.BeDisconnected(); // Ensure this is an async method
+                await _mqttManager.Disconnect(); // Ensure this is an async method
+
+                // Attempt to stop other background work with a timeout
+                var stopTask = StopWorking(); // Assuming StopWorking is an async Task method
+                var delayTask = Task.Delay(TimeSpan.FromSeconds(60));
+                await Task.WhenAny(stopTask, delayTask);
+
+                // Additional cleanup or checks can be performed here
+            }
+            catch (Exception ex)
+            {
+                // Log the exception or show a message to the user
+                MessageBox.Show($"An error occurred while shutting down: {ex.Message}", "Shutdown Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
+            }
+            finally
+            {
+                // Complete the shutdown process by closing the form without further cancellation
+                // Perform this action on the UI thread
+
+                // Check if the form's handle is created before invoking
+                if (this.IsHandleCreated)
+                {
+                    this.Invoke(new Action(() =>
+                    {
+                        // It's safe to close the form now as the shutdown process has been completed or acknowledged with an error
+                        this.Close(); // This call will not re-enter the closing logic due to the isShutdownInitiated flag
+                    }));
+                }
+                else
+                {
+                    // Handle the case where the form's handle is not created
+                    // Log this situation if necessary
+                    // Ensure the program exits
+                    Environment.Exit(0);
+                }
+            }
+        }
+
+        internal void ShowActivationWindow()
+        {
+            var dialog = new FormSubscription();
+            dialog.ShowDialog(Instance);
+
+            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
+            if (dialog.DialogResult != DialogResult.OK)
+                return;
+
+            Cursor.Current = Cursors.WaitCursor;
+            var ipManagerFolder = "C:\\ProgramData\\IPManager";
+            if (System.IO.Directory.Exists(ipManagerFolder))
+                System.IO.Directory.GetFiles(ipManagerFolder, "*").ToList().ForEach(System.IO.File.Delete);
+            LicenseUtility.TryDownloadExistingLicense(dialog.SerialNumber);
+
+            try
+            {
+                var license = LicenseUtility.ReadGenuineLicenseFromFile();
+
+                if (license == null)
+                {
+                    LicenseUtility.ActivateLicense(dialog.SerialNumber);
+                }
+                else
+                {
+                    XtraMessageBox.Show(En_US.Form1_ShowActivationWindow_Please__restart_the_application__);
+                }
+            }
+            catch (Exception)
+            {
+                //ignored
+            }
+
+            Cursor.Current = Cursors.Default;
+        }
+
+        public static void ShowCustomColumnsWindow(object sender, EventArgs e)
+        {
+            var dialog = new FormSpecificsColumns();
+            dialog.ShowDialog();
+        }
+
+        private void zoomTrackBarControl1_EditValueChanged_1(object sender, EventArgs e)
+        {
+            galleryControl1.Gallery.Groups.Last().Gallery.ImageSize = new Size(zoomTrackBarPictures.Value, zoomTrackBarPictures.Value);
+            LastSliderValueSmall = zoomTrackBarPictures.Value;
+        }
+
+        private void barButtonClear_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            _guiChanger.ClearFoundItems();
+        }
+
+        private void barButtonReport_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            if (_formReportDialog != null)
+            {
+                _formReportDialog.WindowState = FormWindowState.Normal;
+                _formReportDialog.Focus();
+            }
+
+            else
+            {
+                _formReportDialog = new FormReportDialog();
+                // formReportDialog.MdiParent = this;
+                _formReportDialog.FormClosed += (o, ea) => { _formReportDialog = null; };
+                _formReportDialog.Show();
+            }
+
+            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
+        }
+
+        private void barButtonBuy_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
+
+            if (selectedRows.Length == 0)
+                return;
+
+            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
+
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
+        }
+
+        private void barButtonSubscriptionInfo_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            ShowActivationWindow();
+        }
+
+        private void barButtonItem3_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var dialog = new FormAbout();
+            dialog.ShowDialog();
+        }
+
+        private void simpleButton1_Click(object sender, EventArgs e)
+        {
+            Stat._errorsCount = 0;
+            memoEditLog.Text = "";
+            barStaticErrorsVal.Caption = "Errors: 0";
+        }
+
+        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
+        {
+            Process.Start(hyperlinkLabelFolder.Tag.ToString());
+        }
+
+        private void dockDescription_CustomButtonClick(object sender, ButtonEventArgs e)
+        {
+            if (e.Button.Properties.Tag.ToString() == "BackgroundColor")
+            {
+                var result = colorDialog1.ShowDialog();
+                if (result == DialogResult.OK)
+                {
+                    // Set form background to the selected color.
+                    UserSettings.BrowserBg = WebView.HexConverter(colorDialog1.Color);
+                    _guiChanger.SetBrowserDocumentText("");
+                }
+            }
+
+            if (e.Button.Properties.Tag.ToString() == "IncreaseFont")
+            {
+                UserSettings.BrowserFontSize += 10;
+
+                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
+            }
+
+            if (e.Button.Properties.Tag.ToString() == "DecreaseFont")
+            {
+                UserSettings.BrowserFontSize = UserSettings.BrowserFontSize - 10;
+                if (UserSettings.BrowserFontSize < 10)
+                    UserSettings.BrowserFontSize = 10;
+
+                WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
+            }
+        }
+
+        #region Browser
+
+        private void zoomTrackBarBrowser_EditValueChanged(object sender, EventArgs e)
+        {
+            if (sender is ZoomTrackBarControl zoomTrackBar)
+            {
+                UserSettings.BrowserFontSize = zoomTrackBar.Value;
+                if (webBrowser1.DocumentText.Length > 0)
+                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
+            }
+
+            if (sender is BarEditItem repositoryZoomTrackBar)
+            {
+                UserSettings.BrowserFontSize = int.Parse(repositoryZoomTrackBar.EditValue.ToString());
+                if (webBrowser1.DocumentText.Length > 0)
+                    WebView.SetZoom(webBrowser1, UserSettings.BrowserFontSize);
+            }
+        }
+
+        private void colorPickBrowser_EditValueChanged(object sender, EventArgs e)
+        {
+            if (sender is BarEditItem colorEdit)
+            {
+                UserSettings.BrowserBg = WebView.HexConverter((Color)colorEdit.EditValue);
+                btnBrowserSettings.Appearance.BackColor = (Color)colorEdit.EditValue;
+                _guiChanger.SetBrowserDocumentText("");
+            }
+
+            if (sender is ColorPickEdit colorPickEdit)
+            {
+                UserSettings.BrowserBg = WebView.HexConverter(colorPickEdit.Color);
+                btnBrowserSettings.Appearance.BackColor = colorPickEdit.Color;
+                _guiChanger.SetBrowserDocumentText("");
+            }
+        }
+
+        #endregion
+
+
+        private static async void _1secTimer_Elapsed(object sender, EventArgs e)
+        {
+            var timer = (System.Windows.Forms.Timer)sender;
+            var elapsedSec = (int)timer.Tag + 1;
+            timer.Tag = elapsedSec;
+
+            RowStatusUpdater.RefreshRowsStatus();
+
+            if (DateTime.UtcNow.Second % 3 == 0)
+                _synchronizationContext.Send(state => Instance.ShowDebugStats(), null);
+
+            if (ProgramState.Idlesw.Elapsed.TotalSeconds > 20 && Upgrader.ImmediateUpgradeAvailable)
+            {
+                Upgrader.ImmediateUpgradeAvailable = false;
+                Upgrader.UpgradeImmediately();
+            }
+
+            GoogleAnalytics?.SendSearchStatsPeriodic();
+
+            var needRestart = Instance._ablyClient.NeedRestart();
+
+            if ((elapsedSec + 15) % 60 == 0 && needRestart)
+            {
+                Debug.WriteLine($"Ably: {elapsedSec} Force disconnect");
+                Instance._ablyClient.Disconnect();
+            }
+
+            if (elapsedSec % 60 == 0 && needRestart)
+            {
+                Debug.WriteLine($"Ably: {elapsedSec} Force connect");
+                if (!ConnectionConfig.MQTTEnabled)
+                    Instance.StartPushClients();
+            }
+        }
+
+        private void _1HourTimer_Elapsed(object sender, EventArgs e)
+        {
+            _updater?.CheckProgramUpdateAvailable();
+            ConnectionConfig.SetApiConfig().ConfigureAwait(false);
+            if (!ConnectionConfig.TradingAPIEnabled)
+            {
+                barButtonEbayAccounts.Appearance.ForeColor = Color.LightGray;
+            }
+
+            LicenseUtility.PeriodicLicenseCheck();
+        }
+
+        private async void _SearchTermSyncTimer_Elapsed(object sender, EventArgs e)
+        {
+            if (!UserSettings.SyncSearchTermsEnabled)
+                return;
+
+
+            var url = UserSettings.SyncSearchTermsUrl;
+            if (string.IsNullOrEmpty(url))
+                return;
+
+            if (Regex.IsMatch(url, SearchTermFetcher.GoogleSpreadsheetRegex))
+                url = SearchTermFetcher.GetCsvExportUrl(url);
+
+            var filePath = await SearchTermFetcher.DownloadKeywordsFileAsync(url);
+            if (string.IsNullOrEmpty(filePath))
+                return;
+
+            var searchTermsFileContent = File.ReadAllText(filePath);
+            var searchTermFileHash = Helpers.Str2Guid(searchTermsFileContent).ToString();
+            if (searchTermFileHash != UserSettings.SyncSearchTermsFileHash)
+            {
+                UserSettings.SyncSearchTermsFileHash = searchTermFileHash;
+                ImportKeywordsFromFile(filePath);
+                if (_searchService is { Running: true })
+                {
+                    await StopWorking();
+                    StartWorking();
+                }
+            }
+        }
+
+        private async void InitEbayAccounts()
+        {
+            await Task
+                .Run(async () =>
+                {
+                    var ebayAccounts = EBayAccountsList.ToList();
+                    foreach (var ebayAccount in ebayAccounts)
+                    {
+                        await IsTokenValid(ebayAccount).ContinueWith(t => Debug.WriteLine("Account checked"));
+                    }
+
+                    EBayAccountInitCompleted = true;
+                })
+                .ContinueWith(t => { });
+
+            Debug.WriteLine("EBayAccountInitCompleted = true");
+        }
+
+        private void InitUpdater()
+        {
+            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Never;
+            _updater = new Updater();
+        }
+
+        private void CreateDefaultSearches()
+        {
+            if (_ebaySearches.ChildrenCore.Count == 0)
+            {
+                NewEbaySearch("iPhone", "iPhone 14 -(128,256,128gb,256gb) -verizon", "9355");
+                NewEbaySearch("GPU 4090", "4090 (MSI,Asus,Gigabyte)", "175673");
+                NewEbaySearch("Lenovo laptop", "Lenovo ThinkPad, Lenovo IdeaPad", "175672");
+                SetTreeListCheckboxesState();
+            }
+
+            for (var i = 0; i < treeList1.Nodes.Count; i++)
+                SetNodeChecked(treeList1.Nodes[i]);
+        }
+
+        private async void InitBGtasks()
+        {
+            _checkItemsStatus = new GetItemsStatus();
+            _checkItemsStatus.StartCheckStatusLoop();
+            StartTelegram();
+            StartPushbullet();
+            barStaticItemTimeDiffText.Caption = $@"eBay - Local time: {await Task.Run(TimeSync.GetTimeSyncDifference)} sec";
+        }
+
+        private void StartPushbullet()
+        {
+            if (string.IsNullOrEmpty(_pushbullet?.Token))
+                return;
+
+            PushBulletListen();
+        }
+
+        private void StartTelegram()
+        {
+            if (string.IsNullOrEmpty(_telegramSender?.TelegramBotID))
+                return;
+
+            _telegramSender?.StartListening();
+        }
+
+        private static async void GetPublicIp()
+        {
+            await Task
+                .Run(() =>
+                {
+                    ProgramState.PublicIp = NetTools.FetchUrl("http://bot.whatismyipaddress.com/");
+                    if (string.IsNullOrEmpty(ProgramState.PublicIp))
+                    {
+                        ProgramState.PublicIp = "************";
+                    }
+                })
+                .ConfigureAwait(false);
+        }
+
+        private void SetupGUI()
+        {
+            layoutControl1.SetDefaultLayout();
+            layoutControlForPicture.SetDefaultLayout();
+            ribbonControl1.Minimized = true;
+            barStaticBuildVersion.Caption = En_US.Form1_Form1_Build__v_ + ProgramState.UBFVersion;
+            hyperlinkLabelFolder.Text = $"App folder: {Folders.Settings}";
+            hyperlinkLabelFolder.Tag = $"{Folders.Settings}";
+            //if (!_isdebug)
+            splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
+            if (Program.AffiliateOff)
+                barStaticErrorsVal.Caption = "Affiliate Off";
+        }
+
+        private void ActiveLookAndFeel_StyleChanged(object sender, EventArgs e)
+        {
+            ChangePanelHeader();
+
+            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
+            foreach (var grView in uniqGrids)
+            {
+                FormatRuleManager.ApplyGridFormatRules(grView);
+            }
+        }
+
+        private static void ChangePanelHeader()
+        {
+            var panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindow];
+            if (panelHeader != null)
+            {
+                panelHeader.ContentMargins.Top = 1;
+                panelHeader.ContentMargins.Bottom = 1;
+            }
+
+            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinTabHeader];
+            if (panelHeader != null)
+            {
+                panelHeader.ContentMargins.Top = 1;
+                panelHeader.ContentMargins.Bottom = 1;
+            }
+
+            panelHeader = DockingSkins.GetSkin(UserLookAndFeel.Default.ActiveLookAndFeel)[DockingSkins.SkinDockWindowBorder];
+
+            panelHeader.ContentMargins.Top = 1;
+            panelHeader.ContentMargins.Bottom = 1;
+            foreach (DockPanel panel in Instance.dockManager1.Panels)
+            {
+                panel.Invalidate();
+                panel.Refresh();
+            }
+
+            LookAndFeelHelper.ForceDefaultLookAndFeelChanged();
+        }
+
+        private void toolTipController2_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
+        {
+            ShowTooltipThumbnal(e);
+        }
+
+        private void ShowTooltipThumbnal(ToolTipControllerGetActiveObjectInfoEventArgs e)
+        {
+            if (e.SelectedControl != gridControl1)
+                return;
+
+            ToolTipControlInfo info = null;
+            var sTooltip1 = new SuperToolTip();
+            try
+            {
+                if (!(gridControl1.GetViewAt(e.ControlMousePosition) is GridView view))
+                    return;
+
+                var hi = view.CalcHitInfo(e.ControlMousePosition);
+                if (hi.HitTest == GridHitTest.RowCell && hi.Column.FieldName == "Thumbnail")
+                {
+                    info = new ToolTipControlInfo(new CellToolTipInfo(hi.RowHandle, hi.Column, "cell"), hi.RowHandle + "," + hi.Column);
+                    if (view.GetRowCellValue(hi.RowHandle, "Thumbnail") is Bitmap cellIm)
+                    {
+                        var item1 = new ToolTipItem();
+                        item1.Image = cellIm;
+                        sTooltip1.Items.Add(item1);
+                    }
+
+                    info = new ToolTipControlInfo(hi.RowHandle + "," + hi.Column, "");
+                    info.SuperTip = sTooltip1;
+                }
+            }
+            catch (Exception)
+            {
+                // ignored
+            }
+            finally
+            {
+                e.Info = info;
+            }
+        }
+
+        #region InfoPanel
+
+        private void lcItemID_Click(object sender, EventArgs e)
+        {
+            object tag;
+            if (!(sender is LabelControl labelControl))
+            {
+                if (!(sender is LayoutControlItem layoutControlItem))
+                    return;
+
+                tag = layoutControlItem.Tag;
+            }
+            else
+            {
+                tag = labelControl.Tag;
+            }
+
+            var ebayAccount = ((DataList)tag)?.EbayAccount;
+            if (ebayAccount != null)
+            {
+                var url = ((DataList)tag).GetAffiliateLink();
+                if (url != null)
+                    Browser.OpenAffiliateLink((DataList)tag);
+            }
+        }
+
+        private void dockItemProperties_CustomButtonClick(object sender, ButtonEventArgs e)
+        {
+            if (e.Button.Properties.Tag.ToString() == "ItemDesciptionOptions")
+            {
+                popupMenuItemDetails.ShowPopup(MousePosition);
+            }
+        }
+
+        private void SetLayoutControlFont(Font f, int delta)
+        {
+            if (layoutControl1.Root.AppearanceItemCaption.FontSizeDelta < 0)
+                layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = 0;
+
+            layoutControl1.Root.AppearanceItemCaption.Font = new Font(f.FontFamily, (float)8.25, f.Style);
+            layoutControl1.Appearance.Control.FontSizeDelta = delta;
+            lcTitle.Font = new Font(lcTitle.Font.FontFamily, layoutControl1.Appearance.Control.Font.Size + 3, FontStyle.Bold);
+        }
+
+        private void btnEditItemProperties_Click(object sender, EventArgs e)
+        {
+            popupMenuItemDetails.ShowPopup(MousePosition);
+        }
+
+        private void btnEditPictureProperties_Click(object sender, EventArgs e)
+        {
+            if (lcipanelPicturesSettingControl.Visibility != DevExpress.XtraLayout.Utils.LayoutVisibility.Never)
+            {
+                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Never;
+            } 
+            else
+            {
+                lcipanelPicturesSettingControl.Visibility = DevExpress.XtraLayout.Utils.LayoutVisibility.Always;
+            }
+        }
+
+        #endregion
+
+        #region Buying buttons
+
+        private void barButtonMakeOffer_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
+
+            if (selectedRows.Length == 0)
+                return;
+
+            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
+
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
+        }
+
+        private void btnMakeOffer_Click(object sender, EventArgs e)
+        {
+            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
+
+            if (selectedRows.Length == 0)
+                return;
+
+            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
+
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
+        }
+
+        private void panelBuyButton_Click_1(object sender, EventArgs e)
+        {
+            if (FocusRouter.FocusedGridView == null)
+            {
+                return;
+            }
+
+            var selectedRows = FocusRouter.FocusedGridView.GetSelectedRows();
+
+            if (selectedRows.Length == 0)
+                return;
+
+            var row = FocusRouter.FocusedGridView.GetDataRow(selectedRows[0]);
+
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
+        }
+
+        #endregion
+
+        private void barButtonClear_ItemDoubleClick(object sender, ItemClickEventArgs e)
+        {
+            var dialogResult = XtraMessageBox.Show(this, "Clear results?", "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
+            if (dialogResult == DialogResult.Yes)
+            {
+                ClearOldResults();
+                barButtonClear.Caption = "Clear results " + DateTime.Now.ToString("HH:mm:ss");
+            }
+        }
+
+        private void ClearOldResults()
+        {
+            _guiChanger.ClearFoundItems();
+            SearchService.Old.Clear();
+            CacheManager.ClearCache();
+            _ebaySearches.ChildrenCore.ForEach(s => s.InitialSearchCount = 0);
+            _telegramSender?.ClearQueue();
+        }
+
+        private void repositoryItemCheckedComboBoxEditCondition_CustomDisplayText(object sender,
+            CustomDisplayTextEventArgs e)
+        {
+            var term = e.Value as string;
+            if (string.IsNullOrEmpty(term))
+            {
+                e.DisplayText = "Any condition";
+            }
+        }
+
+        private void btnClearFilter_Click(object sender, EventArgs e)
+        {
+            popupContainerControl1.OwnerEdit?.CancelPopup();
+        }
+
+        private void barStaticErrorsVal_ItemDoubleClick(object sender, ItemClickEventArgs e)
+        {
+            //XtraMessageBox.Show("Directx: " + GridDirectXEnabled(gridControl1).ToString());
+            if (splitContainerControl2.PanelVisibility == SplitPanelVisibility.Both)
+            {
+                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Panel1;
+            }
+            else
+            {
+                splitContainerControl2.PanelVisibility = SplitPanelVisibility.Both;
+            }
+        }
+
+        private void UpdateRequestCfg()
+        {
+            if (!Visible)
+                return;
+
+            _requestsCfg.FindReqMaxThreads = Convert.ToInt32(spinEditFindReqMaxThreads.Value);
+            if (_requestsCfg.GetItemDetailsReqMaxThreads != Convert.ToInt32(spinEditGetItemDetailsMaxThreads.Value))
+            {
+                _requestsCfg.GetItemDetailsReqMaxThreads = Convert.ToInt32(spinEditGetItemDetailsMaxThreads.Value);
+                _getItemQueueLimiter = new SemaphoreSlim(_requestsCfg.GetItemDetailsReqMaxThreads);
+                BrowseAPIParser.BrowseAPIGetItemQueueLimiter = new SemaphoreSlim(_requestsCfg.GetItemDetailsReqMaxThreads);
+            }
+
+            _requestsCfg.DownloadAvatars = chkDownloadAvatars.Checked;
+            _requestsCfg.DownloadOtherImages = chkDownloadOtherImages.Checked;
+            _requestsCfg.DownloadDescription = chkDownloadDescription.Checked;
+            _requestsCfg.UpdateItemStatus = chkUpdateItemStatusFor2Min.Checked;
+            _requestsCfg.EnabledApi = checkUseAPI.Checked;
+            _requestsCfg.EnabledRss = checkUseRSS.Checked;
+            _requestsCfg.EnabledRss2 = checkUseRSS2.Checked;
+            _requestsCfg.ShowSoldItems = checkEditShowSoldItems.Checked;
+            _requestsCfg.WhiteSpaceAnalyzer = checkEditWhitespaceTokenizer.Checked;
+            if (!_requestsCfg.EnabledApi && !_requestsCfg.EnabledRss && !_requestsCfg.EnabledRss2)
+            {
+                _requestsCfg.EnabledApi = true;
+                MessageBox.Show("Please, enable at least one option - API, RSS or RSS2");
+            }
+        }
+
+        private void _requestCfgChanged(object sender, EventArgs e)
+        {
+            UpdateRequestCfg();
+            SetWhiteSpaceAnalyzer(_requestsCfg.WhiteSpaceAnalyzer);
+        }
+
+        private static void SetWhiteSpaceAnalyzer(bool requestsCfgWhiteSpaceAnalyzer)
+        {
+            KeywordHelpers.SetAnalyzerType(requestsCfgWhiteSpaceAnalyzer);
+        }
+
+        private void Info_Click(object sender, EventArgs e)
+        {
+            XtraMessageBox.Show(@"Find request speed - How many find request to eBay servers per second uBuyFirst actually makes
+
+GetItem queue - after uBuyFirst found a new item, it needs to make one more request to eBay server to get this item's details. If there are many new items, they placed into a queue.
+
+GetItem threads - how many parallel GetItem details requests uBuyFirst can make.
+
+Thumbs - placed into a queue. You may disable it if you don't need them.
+
+Other images - gallery images. You may disable it if you don't need them.
+
+Status update - after new item were added to a grid uBuyFirst checks for 2 minutes whetheer the item where sold. 20 items updates per request. If you have 10 items in queue then 10x20=200 items currently updating its status.
+
+Download description - you may uncheck this if you don't use Description in your filters and Sub Searches.
+
+Use API, RSS, RSS2 - we can look for new items in these 3 sources. Sometimes some of them faster then another.
+
+Show sold items - look in the sold items archive for items that were sold before ubuyfirst found them.
+");
+        }
+
+        #region Gallery
+
+        private void galleryControl1_Gallery_ItemDoubleClick(object sender, GalleryItemClickEventArgs e) => _picManager.GetLargeImages(e.Item, this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);
+
+        private void galleryControl1_Gallery_GalleryItemHover(object sender, GalleryItemEventArgs e)
+        {
+            var galleryItem = e.Item;
+            if (e.Item.Hint == "Error")
+            {
+                var galleryItemGroupCollection = ((GalleryControlGallery)sender).GalleryControl.Gallery.Groups;
+                var galleryItemGroup = galleryItemGroupCollection.Last();
+                var galleryItemCollection = galleryItemGroup.Items;
+                var pictureUrls = new List<string>();
+                foreach (GalleryItem? item in galleryItemCollection)
+                {
+                    var picUrl = item?.Tag?.ToString();
+                    if (!string.IsNullOrEmpty(picUrl))
+                        if (picUrl != null)
+                        {
+                            pictureUrls.Add(picUrl);
+                        }
+                }
+
+                GridViewEvents.GuiChanger.ClearGalleryControl();
+                ImageCache.Inst.ClearCache();
+                GridViewEvents.GuiChanger.UpdatePicPanel(pictureUrls.ToList(), new CancellationToken(false));
+            }
+
+
+            _picManager.GetLargeImages(galleryItem,this.zoomTrackBarExpanded.Value != LastSliderValueLarge, this.zoomTrackBarExpanded.Value);
+        }
+
+        #endregion
+
+        #region Tray Icon
+
+        private void TrayIcon_Click(object sender, MouseEventArgs e)
+        {
+            _trayManager.SetTrayLabel();
+            if (e.Button == MouseButtons.Left)
+                _trayManager.SwitchMinimizedState();
+        }
+
+        private void toolStripMenuItemExit_Click(object sender, EventArgs e)
+        {
+            Application.Exit();
+        }
+
+        private void toolStripMenuItemShow_Click(object sender, EventArgs e)
+        {
+            _trayManager.SwitchMinimizedState();
+        }
+
+        #endregion Tray Icon
+
+        public void ShowUpdateNotification()
+        {
+            barButtonRestartOnUpdate.Visibility = BarItemVisibility.Always;
+        }
+
+        private void BarButtonRestartOnUpdate_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            Upgrader.UpgradeImmediately();
+        }
+
+        private void hyperLinkLabel_Click(object sender, EventArgs e)
+        {
+            var label = (HyperlinkLabelControl)sender;
+            Process.Start(label.Tag.ToString());
+        }
+
+        private void repositoryItemViews_AddingMRUItem(object sender, AddingMRUItemEventArgs e)
+        {
+            return;
+
+            if (UserSettings.CanShowEbaySearchEditor)
+                return;
+
+            if (e.AddReason == MRUItemAddReason.Internal)
+                return;
+
+            if (!(sender is MRUEdit mruEdit))
+                return;
+
+            if (!(mruEdit.Parent is TreeList treeList))
+                return;
+
+            if (!(treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find))
+                return;
+
+            var text = $"Create '{e.Item}' View?\nItems for this eBay search will be displayed in this View";
+            var dialogResult = XtraMessageBox.Show(treeList, text, @"Create View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
+            if (dialogResult != DialogResult.Yes)
+            {
+                e.Cancel = true;
+                mruEdit.SelectedItem = focusedKw2Find.ViewName;
+                mruEdit.CancelPopup();
+                treeList1.CancelCurrentEdit();
+                MruManager.DisableEdit(repositoryItemViews);
+
+                return;
+            }
+
+            UserSettings.CanShowEbaySearchEditor = true;
+            var newViewName = e.Item.ToString();
+            ResultsView.CreateView(newViewName);
+            GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
+            UserSettings.CanShowEbaySearchEditor = false;
+            ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);
+            MruManager.DisableEdit(repositoryItemViews);
+        }
+
+        private void repositoryItemViews_RemovingMRUItem(object sender, RemovingMRUItemEventArgs e)
+        {
+            string removedViewName = e.Item.ToString();
+            if (removedViewName == "Results")
+            {
+                XtraMessageBox.Show(this, "Sorry, you can't remove default view 'Results'");
+                e.Cancel = true;
+                return;
+            }
+
+            var text = $"Delete '{e.Item}' View?\nAll eBay Searches in this view will be assigned to 'Results' View";
+            var dialogResult = XtraMessageBox.Show(text, @"Delete View", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
+            if (dialogResult != DialogResult.Yes)
+            {
+                e.Cancel = true;
+            }
+            else
+            {
+                ResultsView.AssignViewForSearches(removedViewName, "Results", _ebaySearches.ChildrenCore);
+
+                for (int i = 0; i < treeList1.Nodes.Count; i++)
+                {
+                    if (treeList1.GetDataRecordByNode(treeList1.Nodes[i]) is Keyword2Find)
+                    {
+                        if (treeList1.Nodes[i].GetValue("View").ToString() == removedViewName)
+                        {
+                            treeList1.Nodes[i].SetValue("View", @"Results");
+                        }
+                    }
+                }
+
+                repositoryItemViews.Items.Remove(e.Item);
+                var mruEdit = (MRUEdit)sender;
+                if (mruEdit.Parent is TreeList treelist2)
+                    if (treelist2.GetDataRecordByNode(treelist2.FocusedNode) is Keyword2Find focusedKw2Find)
+                    {
+                        mruEdit.SelectedItem = focusedKw2Find.ViewName;
+                        mruEdit.EditValue = focusedKw2Find.ViewName;
+                        mruEdit.Text = focusedKw2Find.ViewName;
+                    }
+
+                var obsoletePanel = dockManager1.Panels.FirstOrDefault(p => p.Tag?.ToString() == removedViewName);
+                obsoletePanel?.Dispose();
+                ResultsView.RemoveFromViewList(removedViewName);
+                mruEdit.Properties.Items.Remove(e.Item);
+                treeList1.Refresh();
+            }
+        }
+
+        private static void RepositoryItemViews_SelectedIndexChanged(object sender, EventArgs e)
+        {
+            var mruEdit = (MRUEdit)sender;
+
+            if (mruEdit.SelectedIndex == -1)
+                return;
+
+            if (!(mruEdit.Parent is TreeList treeList))
+                return;
+
+            if (treeList.GetDataRecordByNode(treeList.FocusedNode) is Keyword2Find focusedKw2Find)
+            {
+                var viewName = mruEdit.SelectedItem.ToString();
+                ResultsView.AssignViewToSearch(viewName, focusedKw2Find);
+            }
+        }
+
+        private void RepositoryItemViews_ButtonClick(object sender, ButtonPressedEventArgs e)
+        {
+            var mruEdit = (MRUEdit)sender;
+            if (e.Button.Caption == "Create View")
+                ShowAddViewDialog(mruEdit);
+
+            return;
+
+            if (e.Button.Caption == "Create View")
+            {
+                switch (mruEdit.Properties.TextEditStyle)
+                {
+                    case TextEditStyles.Standard: //Disable
+                        MruManager.DisableEdit(repositoryItemViews);
+
+                        break;
+                    case TextEditStyles.DisableTextEditor: //Enable
+                        MruManager.EnableEdit(repositoryItemViews);
+                        treeList1.ShowEditor();
+                        treeList1.ActiveEditor.Select();
+
+                        break;
+                }
+            }
+
+            if (e.Button.Caption == "Cancel") //Disable
+            {
+                treeList1.CancelCurrentEdit();
+                MruManager.DisableEdit(repositoryItemViews);
+            }
+        }
+
+        private void ShowAddViewDialog(MRUEdit mruEdit)
+        {
+            XtraInputBoxArgs args = new XtraInputBoxArgs();
+            // set required Input Box options
+            args.Caption = "Create View panel";
+            args.Prompt = "View name";
+            args.DefaultButtonIndex = 0;
+
+            // initialize a DateEdit editor with custom settings
+            var viewNameEdit = new TextEdit();
+            //viewNameEdit.Properties.CalendarView = DevExpress.XtraEditors.Repository.CalendarView.TouchUI;
+            //viewNameEdit.Properties.Mask.EditMask = "MMMM d, yyyy";
+            args.Editor = viewNameEdit;
+
+            // a default DateEdit value
+            if (!(treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find focusedKw2Find))
+                return;
+
+            args.DefaultResponse = focusedKw2Find.Alias;
+            // display an Input Box with the custom editor
+            var show = XtraInputBox.Show(args);
+            if (show != null)
+            {
+                var newViewName = show.ToString();
+                ResultsView.CreateView(newViewName);
+                GridBuilder.CopyLayoutFromExistingResultsGrid(newViewName);
+                UserSettings.CanShowEbaySearchEditor = false;
+                ResultsView.AssignViewToSearch(newViewName, focusedKw2Find);
+
+                MruManager.AddMissingMruItem(newViewName, repositoryItemViews);
+                //  mruEdit.Properties.Items.Add(newViewName);
+
+                foreach (TreeListNode treeList1Node in treeList1.Selection)
+                {
+                    treeList1.RefreshNode(treeList1Node);
+                }
+
+                //mruEdit.SelectedItem = newViewName;
+                //   mruEdit.SelectedIndex = 0;
+                //     mruEdit.EditValue = newViewName;
+            }
+
+            mruEdit.SelectedItem = focusedKw2Find.ViewName;
+            //MruManager.DisableEdit(repositoryItemViews);
+        }
+
+        private void repositoryItemCheckedComboBoxEditListingType_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
+        {
+            var term = e.Value as string;
+            if (string.IsNullOrEmpty(term))
+            {
+                e.DisplayText = "Buy It Now";
+            }
+            else
+            {
+                if (term.Contains(ListingType.BuyItNow.ToString()) && term.Contains(ListingType.AuctionsEndingNow.ToString()) && term.Contains(ListingType.AuctionsStartedNow.ToString()))
+                {
+                    e.DisplayText = "Any";
+                }
+            }
+        }
+
+        private void barEditItemAutoSelect_EditValueChanged(object sender, EventArgs e)
+        {
+            TopRowFocus.TopRowFocusInterval = double.Parse(barEditItemAutoSelect.EditValue.ToString(), CultureInfo.InvariantCulture);
+        }
+
+        private void btnHighlightWords_Click(object sender, EventArgs e)
+        {
+            var formHighlightWords = new FormHighlightWords();
+            formHighlightWords.ShowDialog(this);
+            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
+            SaveSettings();
+        }
+
+        private void btnBrowserSettings_Click(object sender, EventArgs e)
+        {
+            if (flyoutPanelBrowser.IsPopupOpen)
+                flyoutPanelBrowser.HidePopup();
+            else
+            {
+                flyoutPanelBrowser.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
+                flyoutPanelBrowser.Options.Location = new Point(-327, -5);
+                flyoutPanelBrowser.ShowPopup();
+            }
+        }
+
+        private void btnPicturesSettings_Click(object sender, EventArgs e)
+        {
+            //if (flyoutPanel1.IsPopupOpen)
+            //    flyoutPanel1.HidePopup();
+            //else
+            //{
+            //    flyoutPanel1.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Manual;
+            //    flyoutPanel1.Options.Location = new Point(-255, -5);
+            //    flyoutPanel1.ShowPopup();
+            //}
+        }
+
+        private void OpenTagLinkByButton(object sender, ItemClickEventArgs e)
+        {
+            var hyperLink = e.Item;
+            if (hyperLink.Tag != null)
+                Process.Start(hyperLink.Tag.ToString());
+        }
+
+        private async void repositoryItemTreeListLookUpEditCategory_BeforePopup(object sender, EventArgs e)
+        {
+            var treeList = ((TreeListLookUpEdit)sender).Properties.TreeList;
+
+            treeList.AfterCheckNode += TreeList_AfterCheckNode;
+            var siteName = "eBay US";
+
+            /*
+            CategoryLookup.AddColumns(repositoryItemTreeListLookUpEdit1TreeList);
+            var categoryService = new CategoryService(new ApiService(Creds.GetApiContextItem(SiteCodeType.US)));
+
+            repositoryItemTreeListLookUpEditCategory.KeyMember = "CategoryID";
+            repositoryItemTreeListLookUpEditCategory.ValueMember = "CategoryID";
+            repositoryItemTreeListLookUpEditCategory.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
+            repositoryItemTreeListLookUpEdit1TreeList.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
+            repositoryItemTreeListLookUpEditCategory.TreeList.DataSource = categoryService.GetSiteCategories(SiteCodeType.US);
+            //repositoryItemTreeListLookUpEditCategory.KeyMember = "CategoryID";
+            //repositoryItemTreeListLookUpEditCategory.ValueMember = "CategoryID";
+            //repositoryItemTreeListLookUpEditCategory.DisplayMember = "CategoryID";
+            //await CategoryLookup.InitTreelistLookup(repositoryItemTreeListLookUpEditCategory.TreeList, siteName);
+            */
+            await CategoryLookup.InitTreelistLookup(treeList, siteName);
+        }
+
+        private void TreeList_AfterCheckNode(object sender, NodeEventArgs e)
+        {
+            var categories = new List<string>();
+            var treeList = ((TreeList)sender);
+            if (e.Node.CheckState == CheckState.Checked)
+            {
+                foreach (var n in treeList.GetAllCheckedNodes())
+                {
+                    categories.Add(n.GetValue("CategoryID").ToString());
+                }
+            }
+
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
+            {
+                ebaySearch.Categories4Api = string.Join(",", categories);
+                if (treeList1.ActiveEditor != null)
+                    treeList1.ActiveEditor.EditValue = ebaySearch.Categories4Api;
+
+                //repositoryItemTreeListLookUpEditCategory.GetRowByKeyValue()
+            }
+
+            treeList1.Refresh();
+            treeList1.RefreshDataSource();
+        }
+
+        private void repositoryItemTreeListLookUpEditCategory_CustomDisplayText(object sender, CustomDisplayTextEventArgs e)
+        {
+            e.DisplayText = e.Value.ToString();
+
+            return;
+
+            Debug.WriteLine(e.Value);
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
+            {
+                e.DisplayText = ebaySearch.Categories4Api;
+            }
+
+            return;
+
+            //RepositoryItemTreeListLookUpEdit edit = sender as RepositoryItemTreeListLookUpEdit;
+            //object row = edit.TreeList..getrow.GetRowByKeyValue(e.Value);
+            //if (row != null)
+            //{
+            //    string text = string.Empty;
+            //    PropertyDescriptorCollection propertyDescriptors = ListBindingHelper.GetListItemProperties(edit.Properties.DataSource);
+            //    foreach (TreeListColumn column in edit.Properties.TreeList.Columns)
+            //    {
+            //        PropertyDescriptor pd = propertyDescriptors[column.FieldName];
+            //        object val = pd.GetValue(row);
+            //        if (val != null)
+            //            text += val.ToString() + ",";
+            //    }
+            //    e.DisplayText = e.Value.ToString();
+            //}
+        }
+
+        private async void repositoryItemPopupContainerEditCategory_Popup(object sender, EventArgs e)
+        {
+            popupContainerControlCategory.Size = new Size(300, 250);
+            treeListCategory.KeyFieldName = "CategoryID";
+            //treeListCategory.AfterCheckNode -= TreeListCategory_AfterCheckNode;
+            var ebaySite = "eBay US";
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch1)
+            {
+                ebaySite = ebaySearch1.EBaySite.SiteName;
+            }
+
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm1)
+            {
+                ebaySite = childTerm1.GetParent().EBaySite.SiteName;
+            }
+
+            await CategoryLookup
+                .InitTreelistLookup(treeListCategory, ebaySite)
+                .ContinueWith(t =>
+                {
+                    var categoriesList = new List<string>();
+                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
+                    {
+                        categoriesList = ebaySearch.Categories4Api.Split(',').ToList();
+                    }
+
+                    if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
+                    {
+                        categoriesList = childTerm.CategoryIDs.ToList();
+                    }
+
+                    treeListCategory.BeginUpdate();
+                    treeListCategory.UncheckAll();
+
+                    for (var i = 0; i < categoriesList.Count; i++)
+                    {
+                        if (i >= 3)
+                            break;
+
+                        var n = categoriesList[i];
+                        var nodeToCheck = treeListCategory.FindNodeByKeyID(n);
+                        SetParentsCheckState(nodeToCheck, CheckState.Indeterminate);
+                        if (nodeToCheck != null)
+                            treeListCategory.SetNodeCheckState(nodeToCheck, CheckState.Checked);
+                    }
+
+                    treeListCategory.EndUpdate();
+                }, TaskScheduler.FromCurrentSynchronizationContext());
+        }
+
+        private void SetParentsCheckState(TreeListNode nodeToCheck, CheckState checkState)
+        {
+            if (nodeToCheck != null && nodeToCheck.ParentNode != null)
+            {
+                nodeToCheck.ParentNode.CheckState = checkState;
+                SetParentsCheckState(nodeToCheck.ParentNode, checkState);
+            }
+        }
+
+        private void SetChildrenCheckState(TreeListNode nodeToCheck, CheckState checkState)
+        {
+            if (nodeToCheck.Nodes != null)
+                foreach (TreeListNode child in nodeToCheck.Nodes)
+                {
+                    child.CheckState = checkState;
+                    SetChildrenCheckState(child, checkState);
+                }
+        }
+
+        private void TreeListCategory_AfterCheckNode(object sender, NodeEventArgs e)
+        {
+            var treeList = (TreeList)sender;
+
+            var checkedNodes = treeList.GetAllCheckedNodes();
+
+            if (checkedNodes.Count > 3)
+            {
+                e.Node.CheckState = CheckState.Unchecked;
+                checkedNodes = treeList.GetAllCheckedNodes();
+                for (var i = 0; i < checkedNodes.Count; i++)
+                {
+                    if (i >= 3)
+                        checkedNodes[i].CheckState = CheckState.Unchecked;
+                }
+
+                XtraMessageBox.Show(treeList, "Sorry, maximum 3 categories allowed.");
+
+                return;
+            }
+
+            if (e.Node.CheckState == CheckState.Unchecked)
+            {
+                SetParentsCheckState(e.Node, CheckState.Unchecked);
+                SetChildrenCheckState(e.Node, CheckState.Unchecked);
+            }
+
+            var categoriesIds = new List<string>();
+            if (e.Node.CheckState == CheckState.Checked)
+            {
+                SetParentsCheckState(e.Node, CheckState.Indeterminate);
+                SetChildrenCheckState(e.Node, CheckState.Unchecked);
+            }
+
+            for (var i = 0; i < checkedNodes.Count; i++)
+            {
+                if (i >= 3)
+                    checkedNodes[i].CheckState = CheckState.Unchecked;
+                else
+                    categoriesIds.Add(checkedNodes[i].GetValue("CategoryID").ToString());
+            }
+
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find ebaySearch)
+            {
+                ebaySearch.Categories4Api = string.Join(",", categoriesIds);
+                if (treeList1.ActiveEditor != null)
+                {
+                    treeList1.RefreshNode(treeList1.FocusedNode);
+                    treeList1.ActiveEditor.EditValue = ebaySearch.Categories4Api;
+                }
+            }
+
+            if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is ChildTerm childTerm)
+            {
+                childTerm.CategoryIDs = categoriesIds.ToArray();
+                if (treeList1.ActiveEditor != null)
+                {
+                    treeList1.RefreshNode(treeList1.FocusedNode);
+                    treeList1.ActiveEditor.EditValue = string.Join(",", childTerm.CategoryIDs);
+                }
+            }
+        }
+
+        private void barCheckPaypalPayment_CheckedChanged(object sender, ItemClickEventArgs e)
+        {
+            if (barEditItemProfile.EditValue == null || string.IsNullOrWhiteSpace(barEditItemProfile.EditValue.ToString()))
+            {
+                XtraMessageBox.Show("Please, select Chrome profile.");
+                return;
+            }
+
+            CreditCardService.CreditCardPaymentEnabled = ((BarCheckItem)sender).Checked;
+        }
+
+        private void barButtonItemShortcuts_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var formShortcuts = new FormShortcuts();
+            formShortcuts.ShowDialog();
+        }
+
+        private void BarCheckNoConfirmations_CheckedChanged(object sender, ItemClickEventArgs e)
+        {
+            UserSettings.SkipBuyConfirmation = ((BarCheckItem)sender).Checked;
+        }
+
+        private void PopupMenuOpenInBrowser_BeforePopup(object sender, CancelEventArgs e)
+        {
+            var menuItems = CreateOpenInBrowserMenuItems();
+            popupMenuOpenInBrowser.BeginUpdate();
+            popupMenuOpenInBrowser.ItemLinks.Clear();
+            popupMenuOpenInBrowser.ItemLinks.AddRange(menuItems);
+            popupMenuOpenInBrowser.EndUpdate();
+        }
+
+        private void BtnSortFilters_Click(object sender, EventArgs e)
+        {
+            lstchkXfilterList.BeginUpdate();
+            XFilterManager.SortFilters();
+            lstchkXfilterList.EndUpdate();
+        }
+
+        private void barButtonItemTelegram_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var formTelegram = new FormTelegram();
+
+            if (_telegramSender == null)
+                _telegramSender = new TelegramSender();
+            else if (_telegramSender.IsListening())
+                _telegramSender.StopListening();
+            var tmp = new TelegramSender();
+
+            tmp.TelegramAccount = _telegramSender.TelegramAccount;
+            tmp.TelegramBotID = _telegramSender.TelegramBotID;
+            tmp.CombinePushes = _telegramSender.CombinePushes;
+            tmp.BodyColumns = _telegramSender.BodyColumns;
+            tmp.BodyTemplate = _telegramSender.BodyTemplate;
+
+            tmp.Enabled = _telegramSender.Enabled;
+            tmp.SetMaxMessagesPerMinute(_telegramSender.MaxMessagesPerMinute);
+            tmp.PicturesCountToAttach = _telegramSender.PicturesCountToAttach;
+            tmp.TelegramChatID = _telegramSender.TelegramChatID;
+
+            if (!string.IsNullOrEmpty(tmp.TelegramBotID))
+            {
+                try
+                {
+                    tmp.CreateTelegramBotClient(tmp.TelegramBotID);
+                }
+                catch (Exception exception)
+                {
+                    XtraMessageBox.Show(exception.Message);
+                }
+            }
+
+            formTelegram.TelegramTmp = tmp;
+            formTelegram.ebaySearches = _ebaySearches.ChildrenCore;
+            var result = formTelegram.ShowDialog();
+            formTelegram.TelegramTmp.StopListening();
+            if (result == DialogResult.OK)
+            {
+                _telegramSender = formTelegram.TelegramTmp;
+                SaveSettings();
+            }
+
+            if (!_telegramSender.IsListening())
+            {
+                _telegramSender.StartListening();
+            }
+        }
+
+        private void OpenTagLink(object sender, EventArgs e)
+        {
+            Process.Start(((LabelControl)sender).Tag.ToString());
+        }
+
+        private void repositoryItemPopupContainerEditFilter_QueryResultValue(object sender, QueryResultValueEventArgs e)
+        {
+            e.Value = filterControlTerm.FilterCriteria;
+        }
+
+        internal void ToggleCheckoutPermission()
+        {
+            if (ConnectionConfig.CheckoutEnabled || ConnectionConfig.SkipBuyConfirmation)
+            {
+                ribbonPageCheckout.Visible = true;
+            }
+            else
+            {
+                ribbonPageCheckout.Visible = false;
+            }
+
+            if (ConnectionConfig.CheckoutEnabled)
+            {
+                barCheckImmediatePayment.Visibility = BarItemVisibility.Always;
+                barEditItemProfile.Visibility = BarItemVisibility.Always;
+                ribbonPageGroupProfile.Visible = true;
+            }
+            else
+            {
+                barCheckImmediatePayment.Checked = false;
+                barCheckImmediatePayment.Visibility = BarItemVisibility.Never;
+                barEditItemProfile.Visibility = BarItemVisibility.Never;
+                ribbonPageGroupProfile.Visible = false;
+            }
+
+            if (ConnectionConfig.SkipBuyConfirmation)
+            {
+                barCheckNoConfirmations.Visibility = BarItemVisibility.Always;
+            }
+            else
+            {
+                barCheckNoConfirmations.Checked = false;
+                barCheckNoConfirmations.Visibility = BarItemVisibility.Never;
+            }
+        }
+
+        private void barEditItemProfile_EditValueChanged(object sender, EventArgs e)
+        {
+            CookieManager.Profile = (CookieProfile)((BarEditItem)sender).EditValue;
+        }
+
+        private void barStaticLicense_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            ShowActivationWindow();
+        }
+
+        private void barEditItemInitialResultsLimit_EditValueChanged(object sender, EventArgs e)
+        {
+            if (int.TryParse(((BarEditItem)sender).EditValue.ToString(), out var initialResultsLimit))
+            {
+                UserSettings.InitialResultsLimit = initialResultsLimit;
+            }
+        }
+
+        private void workspaceManager1_WorkspaceSaved(object sender, WorkspaceEventArgs args)
+        {
+            if (args.Workspace.Name is "Workspace Active" or "Basic Layout (Default)" or "Full Details (Default)" or "DefaultWorkspace")
+                return;
+            //Analytics.AddEvent("", "WorkspaceSaved", 1);
+        }
+
+        private void barButtonItemIgnoreSellers_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var authForm = new FormIgnoreSellers();
+            var result = authForm.ShowDialog();
+            if (result == DialogResult.OK)
+            {
+                SaveSettings();
+            }
+        }
+
+        private void repositoryItemCheckEditEnabled_EditValueChanged(object sender, EventArgs e)
+        {
+            var treeList = (sender as BaseEdit).Parent as TreeList;
+            var node = treeList.FocusedNode;
+            var isChecked = (bool)(sender as CheckEdit).EditValue;
+            node.Checked = isChecked;
+        }
+
+        private async void barButtonItemRestoreBackup_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            using var openFileDialog = new OpenFileDialog();
+            openFileDialog.Filter = "Backup/config files (*.cfg*, *.zip)|*.cfg*;*.zip";
+            openFileDialog.FilterIndex = 1;
+            openFileDialog.RestoreDirectory = true;
+            openFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
+            openFileDialog.Title = "Restore Backup from ZIP file or Config file";
+            if (openFileDialog.ShowDialog() == DialogResult.OK)
+            {
+                if (_searchService is { Running: true })
+                    await StopWorking();
+
+                var backupFilePath = openFileDialog.FileName;
+                var fileExtension = Path.GetExtension(backupFilePath).ToLower();
+
+                if (fileExtension == ".zip")
+                {
+                    // Extract ZIP file
+                    var tempExtractPath = Path.Combine(Path.GetTempPath(), "uBuyFirst_Backup");
+                    if (Directory.Exists(tempExtractPath))
+                        Directory.Delete(tempExtractPath, true);
+
+                    Directory.CreateDirectory(tempExtractPath);
+                    ZipFile.ExtractToDirectory(backupFilePath, tempExtractPath);
+
+                    // Load settings from extracted files
+                    var configFilePath = Path.Combine(tempExtractPath, "config.cfg");
+                    if (File.Exists(configFilePath))
+                        LoadSettings(configFilePath);
+
+                    var workSpaceFilePath = Path.Combine(tempExtractPath, "Workspace Active.xml");
+                    if (File.Exists(workSpaceFilePath))
+                    {
+                        File.Copy(workSpaceFilePath, Path.Combine(Folders.Workspace, "Workspace Active.xml"), true);
+                        ApplyWorkSpaceOnFormShown();
+                    }
+
+                    var layoutFilePath = Path.Combine(tempExtractPath, "Layout Bid Window.xml");
+                    if (File.Exists(layoutFilePath))
+                        File.Copy(layoutFilePath, Path.Combine(Folders.Layout, "Layout Bid Window.xml"), true);
+                    ClearOldResults();
+                    // Add additional restore logic here for other files if needed
+                    MessageBox.Show("Backup restored from ZIP file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);
+
+                    // Cleanup the temporary extracted files
+                    Directory.Delete(tempExtractPath, true);
+                }
+                else
+                {
+                    // Load the settings from the .cfg file
+                    LoadSettings(backupFilePath);
+                    ClearOldResults();
+                    MessageBox.Show("Backup restored from CFG file successfully!", "Restore", MessageBoxButtons.OK, MessageBoxIcon.Information);
+                }
+            }
+        }
+
+
+        private void barButtonItemCreateBackup_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            // Create a SaveFileDialog to prompt the user where to save the ZIP file
+            using var saveFileDialog = new SaveFileDialog();
+            saveFileDialog.Filter = "ZIP Files (*.zip)|*.zip";
+            saveFileDialog.Title = "Save Backup";
+            saveFileDialog.FileName = $"uBuyFirst Backup {DateTime.Now.ToString("yyyy-MM-dd")}.zip";
+            saveFileDialog.InitialDirectory = Path.Combine(Folders.Backup);
+            // Show the dialog and check if the user clicked OK
+            if (saveFileDialog.ShowDialog() == DialogResult.OK)
+            {
+                // Get the destination path where the ZIP file will be saved
+                var zipFilePath = saveFileDialog.FileName;
+                if (File.Exists(zipFilePath))
+                {
+                    // Delete the existing file
+                    File.Delete(zipFilePath);
+                }
+                BackupToZipFile(zipFilePath);
+
+                MessageBox.Show("Backup created successfully!", "Backup", MessageBoxButtons.OK, MessageBoxIcon.Information);
+            }
+        }
+
+        private static void BackupToZipFile(string zipFilePath)
+        {
+            using (var archive = ZipFile.Open(zipFilePath, ZipArchiveMode.Create))
+            {
+                void AddFileToArchive(string folderPath, string fileName)
+                {
+                    var fullPath = Path.Combine(folderPath, fileName);
+                    if (File.Exists(fullPath))
+                    {
+                        archive.CreateEntryFromFile(fullPath, fileName, CompressionLevel.Optimal);
+                    }
+                }
+
+                AddFileToArchive(Folders.Settings, "config.cfg");
+                AddFileToArchive(Folders.Workspace, "Workspace Active.xml");
+                AddFileToArchive(Folders.Layout, "Layout Bid Window.xml");
+            }
+        }
+        public static void ShowTrayBalloon(string title, string text, int timeout)
+        {
+            if (Form1.Instance.notifyIcon1 != null && Form1.Instance.IsHandleCreated && !Form1.Instance.IsDisposed)
+                Form1.Instance.InvokeIfRequired(() =>
+                {
+                    Form1.Instance.notifyIcon1.BalloonTipTitle = title;
+                    Form1.Instance.notifyIcon1.BalloonTipText = text;
+                    Form1.Instance.notifyIcon1.ShowBalloonTip(timeout * 1000);
+                });
+        }
+
+        private void barCheckItemPriceOpensCheckout_CheckedChanged(object sender, ItemClickEventArgs e)
+        {
+            UserSettings.ClickOnPriceOpensProductPage = !((BarCheckItem)sender).Checked;
+        }
+
+        private void barButtonItemSync_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var formSync = new FormSyncSearchTerms();
+            _searchTermSyncTimer.Stop();
+            formSync.ShowDialog();
+            _searchTermSyncTimer.Interval = (int)TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval).TotalMilliseconds;
+            _searchTermSyncTimer.Start();
+            SaveSettings();
+        }
+
+        private void barButtonItemGetExternalData_ItemClick(object sender, ItemClickEventArgs e)
+        {
+            var externalDataForm = new FormExternalData(_cefBrowserManager);
+            externalDataForm._eBaySearches = _ebaySearches.ChildrenCore;
+            externalDataForm.ShowDialog();
+        }
+
+        private void galleryControl1_Click(object sender, EventArgs e)
+        {
+
+        }
+
+        private void pictureSettingsButton_Click(object sender, EventArgs e)
+        {
+            lock (_chkLargeImagesOnHoverValueChangedLock)
+            {
+                panelPicturesSettingControl.Visible = !panelPicturesSettingControl.Visible;
+            }
+        }
+
+        private void zoomTrackBarExpanded_EditValueChanged(object sender, EventArgs e)
+        {
+        }
+
+    }
+}
diff --git a/EbaySniper/Grid/GridBuilder.cs b/EbaySniper/Grid/GridBuilder.cs
index 099961a0..13631d15 100644
--- a/EbaySniper/Grid/GridBuilder.cs
+++ b/EbaySniper/Grid/GridBuilder.cs
@@ -1,4 +1,13 @@
-﻿using DevExpress.Data;
+﻿using System;
+using System.Collections.Generic;
+using System.ComponentModel;
+using System.Data;
+using System.Diagnostics;
+using System.Drawing;
+using System.IO;
+using System.Linq;
+using System.Windows.Forms;
+using DevExpress.Data;
 using DevExpress.Utils;
 using DevExpress.XtraEditors;
 using DevExpress.XtraEditors.Controls;
@@ -9,15 +18,7 @@ using DevExpress.XtraGrid.Views.BandedGrid;
 using DevExpress.XtraGrid.Views.Base;
 using DevExpress.XtraGrid.Views.Grid;
 using DevExpress.XtraGrid.Views.Grid.ViewInfo;
-using System;
-using System.Collections.Generic;
-using System.ComponentModel;
-using System.Data;
-using System.Diagnostics;
-using System.Drawing;
-using System.IO;
-using System.Linq;
-using System.Windows.Forms;
+using uBuyFirst.AI;
 using uBuyFirst.Data;
 using uBuyFirst.Filters;
 using uBuyFirst.GUI;
@@ -54,9 +55,17 @@ namespace uBuyFirst.Grid
                     continue;
 
                 DefaultDataTable.Columns.Add(specific.CategoryName);
-                ItemSpecifics.AddItemSpecificColumnToGridView(grView, specific.CategoryName);
+                ItemSpecifics.AddCustomColumnToGridView(grView, specific.CategoryName);
             }
 
+            foreach (var aiColumn in AiAnalysis.AiColumnsList)
+            {
+                if (DefaultDataTable.Columns.Contains(aiColumn))
+                    continue;
+
+                DefaultDataTable.Columns.Add(aiColumn);
+                ItemSpecifics.AddCustomColumnToGridView(grView, aiColumn);
+            }
             FormatRuleManager.ApplyGridFormatRules(grView);
 
             return gridControl;
@@ -548,7 +557,7 @@ namespace uBuyFirst.Grid
 
             foreach (GridColumn column in grView.Columns)
             {
-                if (visibleColumnsList.Contains(column.FieldName) || ItemSpecifics.CategorySpecificsList.Count(c => c.CategoryName == column.FieldName) > 0)
+                if (visibleColumnsList.Contains(column.FieldName) || ItemSpecifics.CategorySpecificsList.Count(c => c.CategoryName == column.FieldName) > 0|| AiAnalysis.AiColumnsList.Count(c => c == column.FieldName) > 0)
                 {
                     column.Visible = true;
                 }
diff --git a/EbaySniper/Grid/GridViewEvents.cs b/EbaySniper/Grid/GridViewEvents.cs
index 2df448f0..1c3e6c2e 100644
--- a/EbaySniper/Grid/GridViewEvents.cs
+++ b/EbaySniper/Grid/GridViewEvents.cs
@@ -1,1220 +1,1224 @@
-﻿using System;
-using System.Collections.Generic;
-using System.Data;
-using System.Diagnostics;
-using System.Drawing;
-using System.Drawing.Drawing2D;
-using System.IO;
-using System.Linq;
-using System.Text;
-using System.Text.RegularExpressions;
-using System.Threading.Tasks;
-using System.Windows.Forms;
-using DevExpress.Data;
-using DevExpress.Utils;
-using DevExpress.Utils.Menu;
-using DevExpress.XtraBars.Docking;
-using DevExpress.XtraEditors;
-using DevExpress.XtraGrid;
-using DevExpress.XtraGrid.Views.BandedGrid;
-using DevExpress.XtraGrid.Views.Base;
-using DevExpress.XtraGrid.Views.Grid;
-using DevExpress.XtraGrid.Views.Grid.ViewInfo;
-using eBay.Service.Call;
-using eBay.Service.Core.Soap;
-using uBuyFirst.Data;
-using uBuyFirst.GUI;
-using uBuyFirst.Item;
-using uBuyFirst.Prefs;
-using uBuyFirst.Pricing;
-using uBuyFirst.Properties;
-using uBuyFirst.Purchasing;
-using uBuyFirst.Search;
-using uBuyFirst.Search.Status;
-using uBuyFirst.Tools;
-using uBuyFirst.Views;
-using PopupMenuShowingEventArgs = DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs;
-using uBuyFirst.Images;
-
-namespace uBuyFirst.Grid
-{
-    public static class GridViewEvents
-    {
-        public static GuiChanger GuiChanger;
-
-        // Fields to track the hovered cell
-        internal static int hoveredRowHandle = -1;
-        private static string hoveredColumnFieldName = "";
-
-        public static void gridControl1_Paint(object sender, PaintEventArgs e)
-        {
-            var grView = (AdvBandedGridView)((GridControl)sender).MainView;
-            var viewInfo = grView.GetViewInfo() as GridViewInfo;
-
-            foreach (var row in grView.GetSelectedRows())
-            {
-                if (!grView.IsDataRow(row))
-                    return;
-
-                var rowInfo = viewInfo?.GetGridRowInfo(row);
-
-                if (rowInfo == null)
-                    return;
-
-                var lineStyle = DashStyle.Solid;
-                if (!grView.GridControl.IsFocused)
-                {
-                    lineStyle = DashStyle.Dot;
-                }
-
-                if (!grView.GridControl.IsFocused && grView == FocusRouter.FocusedGridView)
-                {
-                    lineStyle = DashStyle.Solid;
-                }
-
-                var rect = new Rectangle(rowInfo.DataBounds.X, rowInfo.DataBounds.Y, rowInfo.DataBounds.Width, rowInfo.DataBounds.Height - 1);
-                var pen = new Pen(Color.Black, 1);
-                pen.DashStyle = lineStyle;
-                try
-                {
-                    var graphicsCache = grView.GridControl.CreateGraphicsCache();
-                    graphicsCache.DrawRectangle(pen, rect);
-                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Top-2), new Point(rect.Right, rect.Top-2));
-                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Bottom-2), new Point(rect.Right, rect.Bottom-2));
-                }
-                catch (Exception exception)
-                {
-                    Debug.WriteLine(exception);
-                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "gridControl1_Paint Rect:" + rect + "\r\n" + exception.Message);
-                }
-            }
-        }
-
-        public static void gridView1_ShowBands(object sender, EventArgs e)
-        {
-            var grView = (AdvBandedGridView)sender;
-            foreach (GridBand band in grView.Bands)
-            {
-                band.Visible = true;
-            }
-        }
-
-        public static void gridView1_DoubleClick(object sender, EventArgs e)
-        {
-            var grView = (GridView)sender;
-            var pt = grView.GridControl.PointToClient(Control.MousePosition);
-            var info = grView.CalcHitInfo(pt);
-            if (info.InRow || info.InRowCell)
-            {
-                if (info.Column != null)
-                {
-                    var d = (DataList)grView.GetDataRow(info.RowHandle)["Blob"];
-                    Form1.Instance.TopMost = false;
-                    var isNeedToRedirect = DateTimeOffset.UtcNow.AddDays(-1).ToUnixTimeSeconds() > ProgramState.UBuyFirstRedirectTimestamp;
-                    if (isNeedToRedirect && ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 10)
-                    {
-                        ProgramState.UBuyFirstRedirectTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
-                        Browser.OpenAffiliateLinkVia_uBuyFirstRedirect(d);
-                    }
-                    else
-                    {
-                        Browser.OpenAffiliateLink(d);
-                    }
-                }
-            }
-        }
-
-        public static void gridView1_MouseLeave(object sender, EventArgs e)
-        {
-            var grView = (GridView)sender;
-            grView.GridControl.Cursor = Cursors.Default;
-            if (hoveredRowHandle >= 0 && hoveredColumnFieldName == "Seller Name")
-            {
-                hoveredRowHandle = -1;
-                hoveredColumnFieldName = "";
-            }
-        }
-
-        public static bool PauseScrolling = false;
-
-        public static void gridView1_MouseMove(object sender, MouseEventArgs e)
-        {
-            if (!(sender is GridView view))
-                return;
-
-            var hitInfo = view.CalcHitInfo(new Point(e.X, e.Y));
-
-            if (hitInfo.Column == null || !hitInfo.InRowCell)
-            {
-                view.GridControl.Cursor = Cursors.Default;
-            }
-            else
-            {
-                if (hitInfo.Column.Name.Contains("Price"))
-                {
-                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
-                    {
-                        view.GridControl.Cursor = Cursors.Default;
-                    }
-                    else
-                        view.GridControl.Cursor = Cursors.Hand;
-                }
-                else if (hitInfo.Column.Name.Contains("EbayWebsite"))
-                {
-                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
-                    {
-                        view.GridControl.Cursor = Cursors.Default;
-                    }
-                    else
-                        view.GridControl.Cursor = Cursors.Hand;
-                }
-                else if (hitInfo.Column.Name.Contains("FeedbackScore"))
-                {
-                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
-                    {
-                        view.GridControl.Cursor = Cursors.Default;
-                    }
-                    else
-                        view.GridControl.Cursor = Cursors.Hand;
-                }
-                else if (hitInfo.Column.Name.Contains("FeedbackRating"))
-                {
-                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
-                    {
-                        view.GridControl.Cursor = Cursors.Default;
-                    }
-                    else
-                        view.GridControl.Cursor = Cursors.Hand;
-                }
-                else if (hitInfo.Column.Name.Contains("Term"))
-                {
-                    if (view.GetRowCellValue(hitInfo.RowHandle, "Term") is DBNull)
-                    {
-                        view.GridControl.Cursor = Cursors.Default;
-                    }
-                    else
-                    {
-                        var cellValue = view.GetRowCellValue(hitInfo.RowHandle, hitInfo.Column.FieldName)
-                            ?.ToString();
-                        if (!string.IsNullOrEmpty(cellValue))
-                        {
-                            // Regex pattern to match URLs in the text
-                            var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
-                            var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);
-
-                            // Check if the text contains a URL
-                            var match = regex.Match(cellValue);
-                            if (match.Success)
-                                view.GridControl.Cursor = Cursors.Hand;
-                        }
-                    }
-                }
-                else
-                {
-                    view.GridControl.Cursor = Cursors.Default;
-                }
-            }
-
-            if (hitInfo.InRowCell && hitInfo.Column.FieldName == "Seller Name")
-            {
-                // Update previously hovered cell if necessary
-                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
-                {
-                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
-                }
-
-                // Set new hovered cell
-                hoveredRowHandle = hitInfo.RowHandle;
-                hoveredColumnFieldName = hitInfo.Column.FieldName;
-            }
-            else
-            {
-                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
-                {
-                    // Reset only if the mouse moves out of any valid cell area
-                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
-                    hoveredRowHandle = -1;
-                    hoveredColumnFieldName = null;
-                }
-            }
-        }
-
-        public static void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
-        {
-            if (sender is not ColumnView view)
-                return;
-
-            if (e.ListSourceRowIndex == GridControl.InvalidRowHandle)
-                return;
-
-            if (e.Column.FieldName == "Item Price")
-            {
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                {
-                    e.DisplayText = d.ItemPricing.ItemPrice.FormatPrice();
-                    var itemPrice = d.ItemPricing.ItemPrice;
-                    if (!itemPrice.Value.Equals(0.0))
-                        e.DisplayText = itemPrice.FormatPrice();
-                    else
-                        e.DisplayText = "";
-                }
-            }
-
-            if (e.Column.FieldName == "Auction Price")
-            {
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                    if (d.ItemPricing.AuctionPrice.Value.Equals(0.0))
-                    {
-                        e.DisplayText = "";
-                    }
-                    else
-                    {
-                        e.DisplayText = d.ItemPricing.AuctionPrice?.FormatPrice();
-                    }
-            }
-
-            if (e.Column.FieldName == "Shipping")
-            {
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                {
-                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
-                    {
-                        e.DisplayText = "*";
-                    }
-                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
-                    {
-                        e.DisplayText = "N/A";
-                    }
-                    else
-                    {
-                        e.DisplayText = d.ItemShipping.FullSingleShippingPrice.FormatPrice();
-                    }
-                }
-            }
-
-            if (e.Column.FieldName == "Ship Additional Item")
-            {
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
-                        e.DisplayText = "*";
-                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
-                        e.DisplayText = "N/A";
-                    else
-                        e.DisplayText = d.ItemShipping.ShipAdditionalItem.FormatPrice();
-            }
-
-            if (e.Column.FieldName == "Total Price")
-            {
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                {
-                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
-                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
-                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
-                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
-                    else
-                    {
-                        var totalPrice = d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice);
-                        if (totalPrice.Value.Equals(0.0))
-                            e.DisplayText = "";
-                        else
-                            e.DisplayText = totalPrice.FormatPrice();
-                    }
-                }
-            }
-
-            //posted time
-            if (e.Column.FieldName == "Posted Time")
-            {
-                e.Column.SortMode = ColumnSortMode.Value;
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                    e.DisplayText = d.StartTimeLocal?.ToString("HH:mm:ss dd-MMM-yyyy");
-            }
-
-            //found time
-            if (e.Column.FieldName == "Found Time")
-            {
-                e.Column.SortMode = ColumnSortMode.Value;
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                    e.DisplayText = d.FoundTime.ToString();
-            }
-
-            //Sold time
-            if (e.Column.FieldName == "Time Left")
-            {
-                if (e.Value != null && e.Value != DBNull.Value)
-                {
-                    var secondsLeft = (TimeSpan)e.Value;
-                    if (secondsLeft.TotalSeconds < 0)
-                    {
-                        e.DisplayText = "0";
-                    }
-                    else
-                    {
-                        if (secondsLeft.TotalHours >= 24)
-                            e.DisplayText = secondsLeft.ToString(@"d\.hh\:mm\m");
-                        else if (secondsLeft.TotalMinutes >= 60)
-                            e.DisplayText = secondsLeft.ToString(@"hh\:mm\:ss");
-                        else if (secondsLeft.TotalSeconds >= 60)
-                            e.DisplayText = secondsLeft.ToString(@"mm\:ss");
-                        else
-                            e.DisplayText = secondsLeft.ToString("ss");
-                    }
-                }
-            }
-
-            if (e.Column.FieldName == "Sold Time")
-            {
-                if (e.Value != DBNull.Value)
-                {
-                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
-                    if (d?.SoldTime != null)
-                        e.DisplayText = d.SoldTime.ToString();
-                }
-            }
-
-            //posted time
-            if (e.Column.FieldName == "Seller Registration")
-            {
-                e.Column.SortMode = ColumnSortMode.Value;
-                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
-                    if (d.SellerRegistration != null)
-                        e.DisplayText = d.SellerRegistration.ToString("yyyy-MM-dd");
-            }
-
-            if (Debugger.IsAttached && e.Column.FieldName.Contains("Relist"))
-            {
-                if (e.Value != DBNull.Value)
-                {
-                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
-                    e.DisplayText = d.LastStatusCheck.LocalTime.ToString("hh:mm:ss");
-                }
-            }
-
-            if (e.Column.FieldName == "Returns")
-            {
-                if (e.Value != DBNull.Value)
-                {
-                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
-                    if (d?.HighBidder != null && Form1._showHighBidder)
-                    {
-                        e.DisplayText = d.HighBidder;
-                        e.Column.SortMode = ColumnSortMode.DisplayText;
-                    }
-                    else
-                    {
-                        if (e.Value != null)
-                            e.DisplayText = e.Value.ToString();
-                    }
-                }
-            }
-        }
-
-        internal static void gridView1_SelectionChanged(object sender, SelectionChangedEventArgs e)
-        {
-            TopRowFocus.LastFocusedTime = DateTimeOffset.UtcNow;
-        }
-
-        private static void OnDeleteRowClick(object sender, EventArgs e)
-        {
-            var grView = (GridView)((DXMenuItem)sender).Tag;
-            grView.DeleteSelectedRows();
-        }
-
-        private static void OnCheckoutClick(object sender, EventArgs e)
-        {
-            var row = (DataRow)((DXMenuItem)sender).Tag;
-            var d = (DataList)row["Blob"];
-            Browser.LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetCheckoutLink());
-        }
-
-        private static void OnMakeOfferClick(object sender, EventArgs e)
-        {
-            var row = (DataRow)((DXMenuItem)sender).Tag;
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
-        }
-
-        private static void OnBuyItNowClick(object sender, EventArgs e)
-        {
-            var row = (DataRow)((DXMenuItem)sender).Tag;
-            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
-        }
-
-        public static void gridView1_RowCellClick(object sender, RowCellClickEventArgs e)
-        {
-            ProgramState.Idlesw.Restart();
-            var grView = (AdvBandedGridView)sender;
-            var dataRow = grView.GetDataRow(e.RowHandle);
-            if (dataRow?.RowState is not DataRowState.Detached and not DataRowState.Deleted)
-                if (e.Column.FieldName == "ItemID")
-                {
-                    var d = (DataList)dataRow["Blob"];
-                    if (!string.IsNullOrEmpty(d.GetAffiliateLink()))
-                        Browser.OpenAffiliateLink(d);
-
-                    //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
-                }
-
-            if (e.Column.FieldName.Contains("Price") && !string.IsNullOrEmpty(e.CellValue?.ToString()))
-            {
-                //if (!Config.IsAllowed(ProgramState.SerialNumber))
-                //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
-                Placeoffer.ShowPlaceOffer(dataRow, Placeoffer.OrderAction.CommitToBuy,
-                    !UserSettings.ClickOnPriceOpensProductPage);
-            }
-
-            if (e.Column.FieldName == "Seller Name")
-            {
-                var hitInfo = grView.CalcHitInfo(e.Location);
-                // Check if the click is on the correct column
-                if (grView.GetViewInfo() is not GridViewInfo)
-                {
-                    return;
-                }
-
-                // Assuming the icon is drawn on the right side of the cell
-                var cellValueRect = ((GridViewInfo)grView.GetViewInfo())
-                    .GetGridCellInfo(hitInfo.RowHandle, hitInfo.Column).Bounds;
-                var icon = Resources.Blocked; // Your icon resource
-                var iconWidth = icon.Width;
-                var iconHeight = icon.Height;
-
-                // Define the icon's rectangle area within the cell
-                var iconRect = new Rectangle(cellValueRect.Right - (int)iconWidth - 10, // 10 pixels from the right edge
-                    cellValueRect.Y + (cellValueRect.Height - (int)iconHeight) / 2, (int)iconWidth, (int)iconHeight);
-
-                // Check if the click was within the icon's bounds
-                if (iconRect.Contains(e.Location))
-                {
-                    var sellerName = e.CellValue?.ToString();
-                    if (!string.IsNullOrEmpty(sellerName))
-                    {
-                        // Call the method to remove the seller, now that you have the correct name
-                        RemoveSellerFromGridViews(sellerName);
-                    }
-                }
-            }
-
-            if (e.Column.FieldName is "Feedback Score" or "Feedback Rating")
-            {
-                if (grView.GetViewInfo() is not GridViewInfo)
-                {
-                    return;
-                }
-
-                var feedbackCall = new GetFeedbackCall(ConnectionConfig.GetGuestApiContext(SiteCodeType.US));
-                feedbackCall.DetailLevelList = new DetailLevelCodeTypeCollection { DetailLevelCodeType.ReturnAll };
-                feedbackCall.ApiRequest.EndUserIP = "201.201.201." + new Random().Next(1, 200);
-                var sellerName = dataRow["Seller Name"].ToString();
-                feedbackCall.UserID = sellerName;
-                feedbackCall.Execute();
-                var feedbackTable = GetFeedbackSummaryAsText(feedbackCall.FeedbackSummary);
-                var feedbacks = "";
-                foreach (FeedbackDetailType feedback in feedbackCall.FeedbackList)
-                {
-                    var feedbackStyle = "";
-                    var timePassed = DateTime.Now.Subtract(feedback.CommentTime);
-
-                    var timeTxt = "";
-                    if (timePassed.Days > 365)
-                    {
-                        timeTxt = (timePassed.Days / 365).ToString() + "y";
-                    }
-                    else if (timePassed.Days > 30)
-                    {
-                        timeTxt = Math.Round((double)timePassed.Days / 30).ToString() + "mo";
-                    }
-                    else if (timePassed.Days > 7)
-                    {
-                        timeTxt = (timePassed.Days / 7).ToString() + "w";
-                    }
-                    else
-                    {
-                        timeTxt = timePassed.Days.ToString() + "d";
-                    }
-
-                    switch (feedback.CommentType)
-                    {
-                        case CommentTypeCodeType.Positive:
-                            feedbacks += $"[{timeTxt}] {feedback.ItemTitle ?? "+"}\r\n";
-                            break;
-                        case CommentTypeCodeType.Neutral:
-                            feedbacks += $"<backcolor='orange'>[{timeTxt}] {feedback.ItemTitle}</backcolor>\r\n";
-                            break;
-                        case CommentTypeCodeType.Negative:
-                            feedbacks += $"<backcolor=255,102,102>[{timeTxt}] [{feedback.CommentText}] {"\t" + feedback.ItemTitle}</backcolor>\r\n";
-                            break;
-                    }
-                }
-
-                var summaryAsText = $"<b>\r\n{feedbackTable}</b><br>\r\n{feedbacks}";
-
-                var toolTipItem = new ToolTipItem();
-                toolTipItem.Text = summaryAsText;
-                var superToolTip = new SuperToolTip();
-                superToolTip.Items.Add(toolTipItem);
-                superToolTip.AllowHtmlText = DefaultBoolean.True;
-                superToolTip.MaxWidth = 1000;
-                var args = new ToolTipControllerShowEventArgs
-                {
-                    SuperTip = superToolTip,
-                    AllowHtmlText = DefaultBoolean.True,
-                    ToolTipType = ToolTipType.SuperTip,
-                    ToolTipLocation = ToolTipLocation.BottomRight
-                };
-
-                ToolTipController.DefaultController.KeepWhileHovered = true;
-                ToolTipController.DefaultController.AutoPopDelay = 30 * 1000;
-                ToolTipController.DefaultController.AllowHtmlText = true;
-                ToolTipController.DefaultController.Appearance.Font = new Font("Courier New", 10);
-                ToolTipController.DefaultController.ShowHint(args);
-            }
-
-            if (e.Column.FieldName == "Term")
-            {
-                var termText = e.CellValue?.ToString();
-                if (!string.IsNullOrEmpty(termText))
-                {
-                    // Regex pattern to match URLs in the text
-                    var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
-                    var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);
-
-                    // Check if the text contains a URL
-                    var match = regex.Match(termText);
-                    if (match.Success)
-                    {
-                        try
-                        {
-                            // Open the first matched URL in the browser
-                            Process.Start(match.Value);
-                        }
-                        catch (Exception ex)
-                        {
-                            // Handle any exceptions, e.g., log the error or show a message to the user
-                            XtraMessageBox.Show("Failed to open URL: " + ex.Message);
-                        }
-                    }
-                }
-            }
-        }
-
-        public static string GetFeedbackSummaryAsText(FeedbackSummaryType feedbackSummary)
-        {
-            // Set a fixed column width
-            int columnWidth = 7;
-
-            // Table Headers
-            string header = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}", "[1M]", "[6M]", "[12M]");
-
-            // Row for Positive Feedback
-            string positiveRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
-                feedbackSummary.PositiveFeedbackPeriodArray[0].Count,
-                feedbackSummary.PositiveFeedbackPeriodArray[1].Count,
-                feedbackSummary.PositiveFeedbackPeriodArray[2].Count);
-
-            // Row for Neutral Feedback
-            string neutralRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
-                feedbackSummary.NeutralFeedbackPeriodArray[0].Count,
-                feedbackSummary.NeutralFeedbackPeriodArray[1].Count,
-                feedbackSummary.NeutralFeedbackPeriodArray[2].Count);
-
-            // Row for Negative Feedback
-            string negativeRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
-                feedbackSummary.NegativeFeedbackPeriodArray[0].Count,
-                feedbackSummary.NegativeFeedbackPeriodArray[1].Count,
-                feedbackSummary.NegativeFeedbackPeriodArray[2].Count);
-
-            // Combine all rows with line breaks
-            return string.Join(Environment.NewLine, header, $"<color=grey>{positiveRow}</color>", $"<color=orange>{neutralRow}</color>", $"<color=255,102,102>{negativeRow}</color>");
-        }
-
-
-
-
-        private static string PadRight(string text, int length)
-        {
-            return text.PadRight(length);
-        }
-
-        public static string GetFeedbackSummaryAsText2(FeedbackSummaryType feedbackSummary)
-        {
-            StringBuilder summaryText = new StringBuilder();
-
-
-
-            // Negative Feedback Period Array
-            foreach (var feedbackPeriod in feedbackSummary.NegativeFeedbackPeriodArray.ToArray())
-            {
-                summaryText.AppendLine($"Negative: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
-            }
-
-            // Neutral Feedback Period Array
-            foreach (var feedbackPeriod in feedbackSummary.NeutralFeedbackPeriodArray.ToArray())
-            {
-                summaryText.AppendLine($"Neutral Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
-            }
-
-            // Positive Feedback Period Array
-            foreach (var feedbackPeriod in feedbackSummary.PositiveFeedbackPeriodArray.ToArray())
-            {
-                summaryText.AppendLine($"Positive Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
-            }
-
-            // Seller Rating Summary Array
-            foreach (var ratingSummary in feedbackSummary.SellerRatingSummaryArray.ToArray())
-            {
-                foreach (var ratingDetails in ratingSummary.AverageRatingDetails.ToArray())
-                {
-                    summaryText.AppendLine($"Seller Rating - {ratingDetails.RatingDetail}: {ratingDetails.Rating} ({ratingDetails.RatingCount} ratings) in {ratingSummary.FeedbackSummaryPeriod} period.");
-                }
-            }
-
-            // Seller Role Metrics
-            if (feedbackSummary.SellerRoleMetrics != null)
-            {
-                summaryText.AppendLine(
-                    $"Seller Role Metrics - Positive Feedback Left: {feedbackSummary.SellerRoleMetrics.PositiveFeedbackLeftCount}, " +
-                    $"Neutral Feedback Left: {feedbackSummary.SellerRoleMetrics.NeutralFeedbackLeftCount}, " +
-                    $"Negative Feedback Left: {feedbackSummary.SellerRoleMetrics.NegativeFeedbackLeftCount}, " +
-                    $"Feedback Left Percent: {feedbackSummary.SellerRoleMetrics.FeedbackLeftPercent}%, " +
-                    $"Cross Border Transaction Count: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionCount}, " +
-                    $"Cross Border Transaction Percent: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionPercent}%, " +
-                    $"Repeat Buyer Count: {feedbackSummary.SellerRoleMetrics.RepeatBuyerCount}, " +
-                    $"Repeat Buyer Percent: {feedbackSummary.SellerRoleMetrics.RepeatBuyerPercent}%, " +
-                    $"Unique Buyer Count: {feedbackSummary.SellerRoleMetrics.UniqueBuyerCount}, " +
-                    $"Transaction Percent: {feedbackSummary.SellerRoleMetrics.TransactionPercent}%.");
-            }
-
-            // Total Feedback Period Array
-            foreach (var feedbackPeriod in feedbackSummary.TotalFeedbackPeriodArray.ToArray())
-            {
-                summaryText.AppendLine($"Total Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
-            }
-
-            // Unique Feedback Counts
-            summaryText.AppendLine($"Unique Positive Feedback: {feedbackSummary.UniquePositiveFeedbackCount}, " +
-                                   $"Unique Neutral Feedback: {feedbackSummary.UniqueNeutralFeedbackCount}, " +
-                                   $"Unique Negative Feedback: {feedbackSummary.UniqueNegativeFeedbackCount}.");
-
-            return summaryText.ToString();
-        }
-        public static void gridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
-        {
-            var grView = (GridView)sender;
-            if (e.MenuType == GridMenuType.Row)
-            {
-                var rowHandle = e.HitInfo.RowHandle;
-                var row = grView.GetDataRow(rowHandle);
-
-                if (row == null)
-                    return;
-                var d = (DataList)row["Blob"];
-                var menuItemBuyNow = new DXMenuItem("&Buy Now", OnBuyItNowClick, Form1.Instance.barButtonBuy.Glyph);
-                var menuItemCheckout = new DXMenuItem("&Checkout page", OnCheckoutClick, Form1.Instance.barButtonBuy.Glyph);
-
-                if (d.CommitToBuy && !d.Variation)
-                {
-                    var f = menuItemBuyNow.Appearance.Font;
-                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Regular, f.Unit, f.GdiCharSet);
-                    menuItemBuyNow.Appearance.ForeColor = Color.Black;
-                }
-                else
-                {
-                    var f = menuItemBuyNow.Appearance.Font;
-                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Underline, f.Unit, f.GdiCharSet);
-                    menuItemBuyNow.Appearance.ForeColor = Color.Blue;
-
-                    if (e != null)
-                    {
-                        if (e.HitInfo != null)
-                        {
-                            if (e.HitInfo.Column != null)
-                            {
-                                if (e.HitInfo.Column.FieldName != null)
-                                {
-                                }
-                            }
-                        }
-                    }
-                }
-
-                var font = menuItemCheckout.Appearance.Font;
-                menuItemCheckout.Appearance.Font = new Font(font.ToString(), font.Size, FontStyle.Underline, font.Unit, font.GdiCharSet);
-                menuItemCheckout.Appearance.ForeColor = Color.Blue;
-
-                var menuItemBestOffer = new DXMenuItem("&Make Offer", OnMakeOfferClick);
-                menuItemBestOffer.SvgImage = Resources.MakeOffer;
-                if (row["Best Offer"].ToString() == "False")
-                {
-                    menuItemBestOffer.Enabled = false;
-                }
-
-                var menuItemDeleteRow = new DXMenuItem("&Delete Rows (Del)", OnDeleteRowClick, Form1.Instance.imageList16.Images[1]);
-                var menuItemIgnoreSeller = new DXMenuItem("&Ignore Seller", OnIgnoreSellerClick);
-
-                menuItemBuyNow.Tag = row;
-                menuItemCheckout.Tag = row;
-                menuItemBestOffer.Tag = row;
-                menuItemDeleteRow.Tag = grView;
-                menuItemIgnoreSeller.Tag = row;
-                e.Menu.Items.Add(menuItemBuyNow);
-                e.Menu.Items.Add(menuItemCheckout);
-                e.Menu.Items.Add(menuItemBestOffer);
-                e.Menu.Items.Add(menuItemDeleteRow);
-                e.Menu.Items.Add(menuItemIgnoreSeller);
-
-                //var menuItemBuyUsingPaypal = new DXMenuItem("&Buy Using Paypal", OnBuyUsingPaypal);
-                //e.Menu.Items.Add(menuItemBuyUsingPaypal);
-            }
-
-            if (e.MenuType == GridMenuType.Column)
-            {
-                var dxMenuItemExportToFile = new DXMenuItem("Export to Excel", OnExportToExcelClick);
-                dxMenuItemExportToFile.SvgImage = Resources.Export;
-                dxMenuItemExportToFile.Tag = grView;
-                e.Menu.Items.Add(dxMenuItemExportToFile);
-
-                var dxMenuItemResetLayout = new DXMenuItem("Reset Grid Layout", OnResetGridLayoutClick);
-                dxMenuItemResetLayout.SvgImage = Resources.ResetLayout;
-                dxMenuItemResetLayout.Tag = grView;
-                e.Menu.Items.Add(dxMenuItemResetLayout);
-                if (e.Menu.Items.Count > 7)
-                {
-                    e.Menu.Items.Insert(7, new DXMenuItem("Update all items status", UpdateAllItemsStatus));
-                    e.Menu.Items.Insert(7, new DXMenuItem("Add custom columns", Form1.ShowCustomColumnsWindow));
-                }
-            }
-
-            //gridBand1.Visible = true;
-            //gridBand2.Visible = true;
-            //gridBand3.Visible = true;
-        }
-
-        private static void OnIgnoreSellerClick(object sender, EventArgs e)
-        {
-            var row = (DataRow)((DXMenuItem)sender).Tag;
-            if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
-            {
-                return;
-            }
-
-            var d = (DataList)row["Blob"];
-            RemoveSellerFromGridViews(d.SellerName);
-        }
-
-        internal static void RemoveSellerFromGridViews(string sellerName)
-        {
-            //ask user if he is sure
-            var result = XtraMessageBox.Show($"Block '{sellerName}'?", "Block Seller", MessageBoxButtons.YesNo);
-            if (result != DialogResult.Yes)
-                return;
-            UserSettings.BlockedSellers.Add(sellerName);
-            var uniqGrids = GridBuilder.GetUniqGrids(Form1.Instance._ebaySearches.ChildrenCore);
-            foreach (var grView in uniqGrids)
-            {
-                grView.BeginDataUpdate();
-                var dataTable = (DataTable)grView.GridControl.DataSource;
-                var i = 0;
-                while (dataTable.Rows.Count > 0 && i < dataTable.Rows.Count)
-                {
-                    if (dataTable.Rows[i]["Seller Name"].ToString() == sellerName)
-                        dataTable.Rows.RemoveAt(i);
-                    else
-                        i++;
-                }
-
-                grView.EndDataUpdate();
-            }
-        }
-
-        private static async void UpdateAllItemsStatus(object sender, EventArgs e)
-        {
-            /*
-            var updatedItems = GetItemsStatus.FetchBatchStatus(batch);
-            GetItemsStatus.UpdateGrids(batch, updatedItems);
-            new Tuple<SimpleItemType, DataRow>(simpleItem, itemRowPair.Value
-            */
-            var itemDict = new Dictionary<string, DataRow>();
-            foreach (var view in ResultsView.ViewsDict)
-            {
-                var datatable = (DataTable)view.Value.DataSource;
-                var i = 0;
-                while (i < datatable?.Rows.Count)
-                {
-                    var row = datatable.Rows[i];
-                    var dataList = (DataList)row["Blob"];
-
-                    if (dataList.Status != Status.Active && dataList.Status != Status.Unknown && dataList.Status != Status.Updated)
-                    {
-                        i++;
-
-                        continue;
-                    }
-
-                    itemDict.Add(dataList.ItemID, row);
-                    i++;
-                }
-            }
-
-            var batches = itemDict.Batch(20);
-
-            //foreach (IEnumerable<KeyValuePair<string, DataRow>> batch in batches)
-            //{
-            //    var simpleItems = await Task.Run(() => GetItemsStatus.FetchBatchStatus(batch.ToList().Select(pair => pair.Key).ToList()));
-            //    foreach (var simpleItem in simpleItems)
-            //    {
-            //        if (itemDict.TryGetValue(simpleItem.ItemID, out var row))
-            //        {
-            //            ((DataList)row["Blob"]).LastStatusCheck = new Time.DateTimeWithDiff()
-            //            {
-            //                TimeDiff = 0,
-            //                Utc = DateTime.UtcNow
-            //            };
-
-            //            if (!Form1.Instance.IsDisposed)
-            //                GetItemsStatus.ListingStatusChanged(new Tuple<SimpleItemType, DataRow>(simpleItem, row));
-            //        }
-            //    }
-            //}
-            foreach (var iEnumerable in batches)
-            {
-                var batch = iEnumerable.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
-                var updatedItems = await Task.Run(() => GetItemsStatus.FetchBatchStatusBrowseAPI(batch.Select(pair => pair.Key).ToList()));
-                if (updatedItems != null)
-                {
-                    GetItemsStatus.UpdateGrids(batch, updatedItems);
-                }
-            }
-        }
-
-        public static void gridView1_RowDeleted(object sender, RowDeletedEventArgs e)
-        {
-            if (e.RowHandle == 0)
-            {
-                UpdateDataOnRowChange((AdvBandedGridView)sender, e.RowHandle);
-            }
-
-            if (((AdvBandedGridView)sender).RowCount == 0)
-            {
-                GuiChanger.ClearItemInfoOnZeroRows();
-            }
-        }
-
-        public static void gridView1_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
-        {
-            if (e.FocusedRowHandle == GridControl.InvalidRowHandle)
-                return;
-            //LastTopRowHandle = ((AdvBandedGridView)sender).FocusedRowHandle - ((AdvBandedGridView)sender).TopRowIndex;
-            var gridView = ((AdvBandedGridView)sender);
-            var firstVisibleRow = gridView.GetVisibleRowHandle(0);
-            if (rowCount < gridView.RowCount)
-            {
-                if (e.FocusedRowHandle - e.PrevFocusedRowHandle == 1)
-                {
-                    if (gridView.GridControl.Cursor == Cursors.Hand)
-                    {
-                        gridView.TopRowIndex++;
-
-
-                        var dataRow = FocusRouter.FocusedGridView.GetDataRow(0);
-
-                        if (dataRow == null)
-                            return;
-                        var args = gridView.GridControl.ToolTipController.CreateShowArgs();
-                        args.ToolTip = dataRow["Title"].ToString();
-                        args.IconType = ToolTipIconType.None;
-                        args.ImageIndex = -1;
-                        args.IconSize = ToolTipIconSize.Small;
-                        args.ToolTipAnchor = ToolTipAnchor.Object;
-
-                        GridView view = gridView;
-                        var info = view.GetViewInfo() as GridViewInfo;
-                        if (info != null)
-                        {
-                            var colInfo = info.ColumnsInfo;
-
-                            var titleColumn = view.Columns["Title"];
-                            if (titleColumn != null && colInfo != null)
-                            {
-                                if (gridView.GridControl?.Parent?.Parent?.Parent == null)
-                                    return;
-
-                                var isFloatingPanel = gridView.GridControl.Parent.Parent.Parent.Parent == null;
-                                var gridColumnInfoArgs = colInfo[titleColumn];
-                                if (gridColumnInfoArgs == null)
-                                {
-                                    return;
-                                }
-
-                                var rect = gridColumnInfoArgs.Bounds;
-
-                                var xFormLocation = 0;
-                                var xPanelLocation = 0;
-                                var xColumn = rect.X;
-                                var yFormLocation = 0;
-                                var yPanelLocation = 0;
-                                var yGridLocation = 0;
-
-                                if (isFloatingPanel)
-                                {
-                                    if (gridView.GridControl.Parent.Parent is DockPanel dockPanel)
-                                    {
-                                        xPanelLocation = dockPanel.FloatLocation.X;
-                                        yPanelLocation = dockPanel.FloatLocation.Y + 5;
-                                    }
-                                }
-                                else
-                                {
-                                    xFormLocation = Form1.Instance.Location.X;
-                                    xPanelLocation = gridView.GridControl.Parent.Parent.Parent.Location.X;
-                                    yFormLocation = Form1.Instance.Location.Y;
-                                    yPanelLocation = gridView.GridControl.Parent.Parent.Parent.Parent.Location.Y;
-                                    yGridLocation = gridView.GridControl.Location.Y;
-                                }
-
-                                var cursorPosition = new Point(xFormLocation + xPanelLocation + 5 + xColumn, yFormLocation + yPanelLocation + yGridLocation);
-                                gridView.GridControl.ToolTipController.ShowHint(args, cursorPosition);
-                            }
-                        }
-                    }
-                }
-            }
-
-            //rowCount = gridView.RowCount;
-            //gridView.OptionsNavigation.AutoFocusNewRow = true; 
-            gridView.OptionsBehavior.KeepFocusedRowOnUpdate = false;
-            //gridView.OptionsBehavior.ImmediateUpdateRowPosition = true;
-            //Debug.WriteLine("LastTopRowHandle:" + e.FocusedRowHandle + "/" + (e.FocusedRowHandle - e.PrevFocusedRowHandle) + "/" + gridView.GridControl.Cursor);
-            //if (e.FocusedRowHandle - e.PrevFocusedRowHandle == 1)
-            //((AdvBandedGridView) sender).TopRowIndex++;            
-            UpdateDataOnRowChange((AdvBandedGridView)sender, e.FocusedRowHandle);
-        }
-
-        public static bool GridUpDownHold = false;
-        public static bool FocusedRowChanged = false;
-
-        public static void UpdateDataOnRowChange(AdvBandedGridView grView, int focusedRowHandle)
-        {
-            FocusedRowChanged = true;
-
-            if (GridUpDownHold)
-                return;
-
-            if (grView != FocusRouter.FocusedGridView)
-                return;
-
-            var row = GetDataRow(grView, focusedRowHandle);
-
-            if (row == null)
-                return;
-
-            var d = (DataList)row["Blob"];
-            if (ProgramState.LastSelectedItemID != d.ItemID || string.IsNullOrEmpty(ProgramState.LastSelectedItemID))
-            {
-                try
-                {
-                    var ctRowChange = GuiChanger.ResetCancelToken();
-                    ProgramState.LastSelectedItemID = d.ItemID;
-                    //if (!Form1.Instance.layoutControl1.IsUpdateLocked)
-                    Form1.Instance.layoutControl1.BeginUpdate();
-                    try
-                    {
-                        GuiChanger.UpdateItemDetailsPanel(d);
-                        ItemSpecifics.UpdateItemDetailsPanelSpecifics(grView, row, Form1.Instance.layoutControl1);
-                    }
-                    catch (Exception ex)
-                    {
-                        ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
-                    }
-
-                    Form1.Instance.layoutControl1.EndUpdate();
-                    GuiChanger.UpdateButtons(d);
-
-                    GuiChanger.ClearGalleryControl();
-                    ImageCache.Inst.ClearCache();
-
-
-                    var pics = new List<string> { d.GalleryUrl };
-                    pics.AddRange(d.Pictures);
-                    GuiChanger.UpdatePicPanel(pics, ctRowChange);
-                    GuiChanger.UpdateExternalData(UserSettings.ExternalEndpointUrl, d.Row);
-                    if (Form1.Instance.dockDescription.Visibility != DockVisibility.Visible
-                        || d.SellerName.Contains("kathe_laz")
-                        || d.SellerName.Contains("wirelessmobilepc")
-                        || d.SellerName.Contains("oklets1"))
-                        return;
-
-                    GuiChanger.UpdateBrowser(d.ItemID, row, Form1.Instance.CancelTokenOnRowChange);
-                }
-                catch (TaskCanceledException)
-                {
-                }
-                catch (OperationCanceledException)
-                {
-                }
-                catch (FileNotFoundException)
-                {
-                }
-                catch (RowNotInTableException)
-                {
-                }
-                catch (IOException ioex)
-                {
-                    if (Debugger.IsAttached)
-                    {
-                        MessageBox.Show(@"Update Avatar: " + ioex.Message);
-                    }
-                }
-                catch (ArgumentNullException anex)
-                {
-                    if (anex.Message != "Additional information: Value cannot be nul")
-                        ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
-                }
-                catch (Exception ex)
-                {
-                    ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
-                }
-            }
-        }
-
-        private static DataRow GetDataRow(AdvBandedGridView grView, int focusedRowHandle)
-        {
-            DataRow row = null;
-            try
-            {
-                row = grView.GetDataRow(focusedRowHandle);
-
-                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
-                    return null;
-            }
-            catch (ArgumentNullException anex)
-            {
-                if (anex.Message != "Value cannot be null.\r\nParameter name: key")
-                    ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
-            }
-            catch (Exception)
-            {
-                // ignored
-            }
-
-            return row;
-        }
-
-        public static void GridView1_GotFocus(object sender, EventArgs e)
-        {
-            FocusRouter.FocusedGridView = (GridView)sender;
-            if (FocusRouter.FocusedGridView.RowCount == 1)
-            {
-                if (FocusRouter.FocusedGridView.FocusedRowHandle == GridControl.InvalidRowHandle)
-                    return;
-
-                //UpdateDataOnRowChange((AdvBandedGridView)sender, FocusRouter.FocusedGridView.FocusedRowHandle);
-            }
-        }
-
-        public static void GridView1_Click(object sender, EventArgs e)
-        {
-            if (FocusRouter.FocusedGridView != ((GridView)sender))
-            {
-                FocusRouter.FocusedGridView = ((GridView)sender);
-                FocusRouter.FocusedGridView.InvalidateRow(FocusRouter.FocusedGridView.FocusedRowHandle);
-            }
-        }
-
-        public static void OnResetGridLayoutClick(object sender, EventArgs e)
-        {
-            var result = XtraMessageBox.Show("Are you sure?\nGrid settings like columns size, order, visibility, etc. will be set to a default state", "", MessageBoxButtons.OKCancel);
-            if (result == DialogResult.OK)
-            {
-                var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
-                var datasource = grView.GridControl.DataSource;
-                grView.GridControl.DataSource = null;
-                grView.Columns.Clear();
-                grView.GridControl.DataSource = datasource;
-                GridBuilder.ResetGridViewLayout(grView);
-            }
-        }
-
-        private static string _exportFolderLocation = "";
-
-        private static void OnExportToExcelClick(object sender, EventArgs e)
-        {
-            var dialog = new FolderBrowserDialog();
-            dialog.Description = "Export to folder";
-            if (!string.IsNullOrWhiteSpace(_exportFolderLocation))
-                dialog.SelectedPath = _exportFolderLocation;
-            var result = dialog.ShowDialog();
-            if (result != DialogResult.OK)
-                return;
-
-            _exportFolderLocation = dialog.SelectedPath;
-            var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
-            var fileName = $"uBuyFirst Export {DateTime.Now.ToString("u").Replace(":", ".").Replace("Z", "")}.csv";
-            var exportPath = Path.Combine(_exportFolderLocation, fileName);
-            var exportRows = new List<String>();
-            var headers = grView.VisibleColumns.Select(c => c.FieldName.ToString()).ToList();
-            var headerRow = Helpers.CreateCSVRow(headers);
-            exportRows.Add(headerRow);
-            for (var i = 0; i < grView.RowCount; i++)
-            {
-                var exportCells = new List<String>();
-                foreach (var header in headers)
-                {
-                    var cellText = grView.GetRowCellDisplayText(i, header);
-                    exportCells.Add(cellText);
-                }
-
-                var properRow = Helpers.CreateCSVRow(exportCells);
-                exportRows.Add(properRow);
-            }
-
-            var fileContents = string.Join("\r\n", exportRows);
-            File.WriteAllText(exportPath, fileContents);
-            Process.Start(exportPath);
-        }
-
-        private static int rowCount;
-
-        public static void gridView1_RowCountChanged(object sender, EventArgs e)
-        {
-            var grView = (AdvBandedGridView)sender;
-            if (grView.RowCount == 1)
-            {
-                TopRowFocus.FocusTopRow(grView, 0);
-            }
-
-            if (rowCount < grView.RowCount)
-            {
-                //grView.TopRowIndex++;//= grView.RowCount - rowCount;
-            }
-
-            //rowCount = grView.RowCount;
-        }
-
-        public static void gridView1_TopRowChanged(object sender, EventArgs e)
-        {
-            return;
-
-            var gridView = ((AdvBandedGridView)sender);
-            if (rowCount < gridView.RowCount)
-            {
-                gridView.TopRowIndex++; //= grView.RowCount - rowCount;
-            }
-
-            rowCount = gridView.RowCount;
-        }
-
-        public static void GridView1_FormatRuleDataUpdateCustomTrigger(object sender, FormatRuleGridDataUpdateTriggerEventArgs e)
-        {
-            if (double.TryParse(e.NewValue.ToString(), out var price))
-            {
-                if (price > 0)
-                {
-                    e.Trigger = true;
-                    return;
-                }
-            }
-
-            e.Trigger = false;
-        }
-    }
-}
+﻿using System;
+using System.Collections.Generic;
+using System.Data;
+using System.Diagnostics;
+using System.Drawing;
+using System.Drawing.Drawing2D;
+using System.IO;
+using System.Linq;
+using System.Text;
+using System.Text.RegularExpressions;
+using System.Threading.Tasks;
+using System.Windows.Forms;
+using DevExpress.Data;
+using DevExpress.Utils;
+using DevExpress.Utils.Menu;
+using DevExpress.XtraBars.Docking;
+using DevExpress.XtraEditors;
+using DevExpress.XtraGrid;
+using DevExpress.XtraGrid.Views.BandedGrid;
+using DevExpress.XtraGrid.Views.Base;
+using DevExpress.XtraGrid.Views.Grid;
+using DevExpress.XtraGrid.Views.Grid.ViewInfo;
+using eBay.Service.Call;
+using eBay.Service.Core.Soap;
+using uBuyFirst.AI;
+using uBuyFirst.Data;
+using uBuyFirst.GUI;
+using uBuyFirst.Item;
+using uBuyFirst.Prefs;
+using uBuyFirst.Pricing;
+using uBuyFirst.Properties;
+using uBuyFirst.Purchasing;
+using uBuyFirst.Search;
+using uBuyFirst.Search.Status;
+using uBuyFirst.Tools;
+using uBuyFirst.Views;
+using PopupMenuShowingEventArgs = DevExpress.XtraGrid.Views.Grid.PopupMenuShowingEventArgs;
+using uBuyFirst.Images;
+
+namespace uBuyFirst.Grid
+{
+    public static class GridViewEvents
+    {
+        public static GuiChanger GuiChanger;
+
+        // Fields to track the hovered cell
+        internal static int hoveredRowHandle = -1;
+        private static string hoveredColumnFieldName = "";
+
+        public static void gridControl1_Paint(object sender, PaintEventArgs e)
+        {
+            var grView = (AdvBandedGridView)((GridControl)sender).MainView;
+            var viewInfo = grView.GetViewInfo() as GridViewInfo;
+
+            foreach (var row in grView.GetSelectedRows())
+            {
+                if (!grView.IsDataRow(row))
+                    return;
+
+                var rowInfo = viewInfo?.GetGridRowInfo(row);
+
+                if (rowInfo == null)
+                    return;
+
+                var lineStyle = DashStyle.Solid;
+                if (!grView.GridControl.IsFocused)
+                {
+                    lineStyle = DashStyle.Dot;
+                }
+
+                if (!grView.GridControl.IsFocused && grView == FocusRouter.FocusedGridView)
+                {
+                    lineStyle = DashStyle.Solid;
+                }
+
+                var rect = new Rectangle(rowInfo.DataBounds.X, rowInfo.DataBounds.Y, rowInfo.DataBounds.Width, rowInfo.DataBounds.Height - 1);
+                var pen = new Pen(Color.Black, 1);
+                pen.DashStyle = lineStyle;
+                try
+                {
+                    var graphicsCache = grView.GridControl.CreateGraphicsCache();
+                    graphicsCache.DrawRectangle(pen, rect);
+                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Top-2), new Point(rect.Right, rect.Top-2));
+                    //graphicsCache.DrawLine(pen, new Point(rect.Left, rect.Bottom-2), new Point(rect.Right, rect.Bottom-2));
+                }
+                catch (Exception exception)
+                {
+                    Debug.WriteLine(exception);
+                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "gridControl1_Paint Rect:" + rect + "\r\n" + exception.Message);
+                }
+            }
+        }
+
+        public static void gridView1_ShowBands(object sender, EventArgs e)
+        {
+            var grView = (AdvBandedGridView)sender;
+            foreach (GridBand band in grView.Bands)
+            {
+                band.Visible = true;
+            }
+        }
+
+        public static void gridView1_DoubleClick(object sender, EventArgs e)
+        {
+            var grView = (GridView)sender;
+            var pt = grView.GridControl.PointToClient(Control.MousePosition);
+            var info = grView.CalcHitInfo(pt);
+            if (info.InRow || info.InRowCell)
+            {
+                if (info.Column != null)
+                {
+                    var d = (DataList)grView.GetDataRow(info.RowHandle)["Blob"];
+                    Form1.Instance.TopMost = false;
+                    var isNeedToRedirect = DateTimeOffset.UtcNow.AddDays(-1).ToUnixTimeSeconds() > ProgramState.UBuyFirstRedirectTimestamp;
+                    if (isNeedToRedirect && ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 10)
+                    {
+                        ProgramState.UBuyFirstRedirectTimestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
+                        Browser.OpenAffiliateLinkVia_uBuyFirstRedirect(d);
+                    }
+                    else
+                    {
+                        Browser.OpenAffiliateLink(d);
+                    }
+                }
+            }
+        }
+
+        public static void gridView1_MouseLeave(object sender, EventArgs e)
+        {
+            var grView = (GridView)sender;
+            grView.GridControl.Cursor = Cursors.Default;
+            if (hoveredRowHandle >= 0 && hoveredColumnFieldName == "Seller Name")
+            {
+                hoveredRowHandle = -1;
+                hoveredColumnFieldName = "";
+            }
+        }
+
+        public static bool PauseScrolling = false;
+
+        public static void gridView1_MouseMove(object sender, MouseEventArgs e)
+        {
+            if (!(sender is GridView view))
+                return;
+
+            var hitInfo = view.CalcHitInfo(new Point(e.X, e.Y));
+
+            if (hitInfo.Column == null || !hitInfo.InRowCell)
+            {
+                view.GridControl.Cursor = Cursors.Default;
+            }
+            else
+            {
+                if (hitInfo.Column.Name.Contains("Price"))
+                {
+                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
+                    {
+                        view.GridControl.Cursor = Cursors.Default;
+                    }
+                    else
+                        view.GridControl.Cursor = Cursors.Hand;
+                }
+                else if (hitInfo.Column.Name.Contains("EbayWebsite"))
+                {
+                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
+                    {
+                        view.GridControl.Cursor = Cursors.Default;
+                    }
+                    else
+                        view.GridControl.Cursor = Cursors.Hand;
+                }
+                else if (hitInfo.Column.Name.Contains("FeedbackScore"))
+                {
+                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
+                    {
+                        view.GridControl.Cursor = Cursors.Default;
+                    }
+                    else
+                        view.GridControl.Cursor = Cursors.Hand;
+                }
+                else if (hitInfo.Column.Name.Contains("FeedbackRating"))
+                {
+                    if (view.GetRowCellValue(hitInfo.RowHandle, "Item Price") is DBNull)
+                    {
+                        view.GridControl.Cursor = Cursors.Default;
+                    }
+                    else
+                        view.GridControl.Cursor = Cursors.Hand;
+                }
+                else if (hitInfo.Column.Name.Contains("Term"))
+                {
+                    if (view.GetRowCellValue(hitInfo.RowHandle, "Term") is DBNull)
+                    {
+                        view.GridControl.Cursor = Cursors.Default;
+                    }
+                    else
+                    {
+                        var cellValue = view.GetRowCellValue(hitInfo.RowHandle, hitInfo.Column.FieldName)
+                            ?.ToString();
+                        if (!string.IsNullOrEmpty(cellValue))
+                        {
+                            // Regex pattern to match URLs in the text
+                            var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
+                            var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);
+
+                            // Check if the text contains a URL
+                            var match = regex.Match(cellValue);
+                            if (match.Success)
+                                view.GridControl.Cursor = Cursors.Hand;
+                        }
+                    }
+                }
+                else
+                {
+                    view.GridControl.Cursor = Cursors.Default;
+                }
+            }
+
+            if (hitInfo.InRowCell && hitInfo.Column.FieldName == "Seller Name")
+            {
+                // Update previously hovered cell if necessary
+                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
+                {
+                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
+                }
+
+                // Set new hovered cell
+                hoveredRowHandle = hitInfo.RowHandle;
+                hoveredColumnFieldName = hitInfo.Column.FieldName;
+            }
+            else
+            {
+                if (hoveredRowHandle >= 0 && hoveredColumnFieldName != null)
+                {
+                    // Reset only if the mouse moves out of any valid cell area
+                    view.RefreshRowCell(hoveredRowHandle, view.Columns["Seller Name"]);
+                    hoveredRowHandle = -1;
+                    hoveredColumnFieldName = null;
+                }
+            }
+        }
+
+        public static void gridView1_CustomColumnDisplayText(object sender, CustomColumnDisplayTextEventArgs e)
+        {
+            if (sender is not ColumnView view)
+                return;
+
+            if (e.ListSourceRowIndex == GridControl.InvalidRowHandle)
+                return;
+
+            if (e.Column.FieldName == "Item Price")
+            {
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                {
+                    e.DisplayText = d.ItemPricing.ItemPrice.FormatPrice();
+                    var itemPrice = d.ItemPricing.ItemPrice;
+                    if (!itemPrice.Value.Equals(0.0))
+                        e.DisplayText = itemPrice.FormatPrice();
+                    else
+                        e.DisplayText = "";
+                }
+            }
+
+            if (e.Column.FieldName == "Auction Price")
+            {
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                    if (d.ItemPricing.AuctionPrice.Value.Equals(0.0))
+                    {
+                        e.DisplayText = "";
+                    }
+                    else
+                    {
+                        e.DisplayText = d.ItemPricing.AuctionPrice?.FormatPrice();
+                    }
+            }
+
+            if (e.Column.FieldName == "Shipping")
+            {
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                {
+                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
+                    {
+                        e.DisplayText = "*";
+                    }
+                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
+                    {
+                        e.DisplayText = "N/A";
+                    }
+                    else
+                    {
+                        e.DisplayText = d.ItemShipping.FullSingleShippingPrice.FormatPrice();
+                    }
+                }
+            }
+
+            if (e.Column.FieldName == "Ship Additional Item")
+            {
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
+                        e.DisplayText = "*";
+                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
+                        e.DisplayText = "N/A";
+                    else
+                        e.DisplayText = d.ItemShipping.ShipAdditionalItem.FormatPrice();
+            }
+
+            if (e.Column.FieldName == "Total Price")
+            {
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                {
+                    if (d.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Fetching || d.ItemShipping.ShippingStatusBrowseAPI == ItemShipping.ParsingStatus.Fetching)
+                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
+                    else if (d.ItemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Success && d.ItemShipping.ShippingStatusBrowseAPI != ItemShipping.ParsingStatus.Success)
+                        e.DisplayText = $"{d.ItemPricing.ItemPrice.FormatPrice()} *";
+                    else
+                    {
+                        var totalPrice = d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice);
+                        if (totalPrice.Value.Equals(0.0))
+                            e.DisplayText = "";
+                        else
+                            e.DisplayText = totalPrice.FormatPrice();
+                    }
+                }
+            }
+
+            //posted time
+            if (e.Column.FieldName == "Posted Time")
+            {
+                e.Column.SortMode = ColumnSortMode.Value;
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                    e.DisplayText = d.StartTimeLocal?.ToString("HH:mm:ss dd-MMM-yyyy");
+            }
+
+            //found time
+            if (e.Column.FieldName == "Found Time")
+            {
+                e.Column.SortMode = ColumnSortMode.Value;
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                    e.DisplayText = d.FoundTime.ToString();
+            }
+
+            //Sold time
+            if (e.Column.FieldName == "Time Left")
+            {
+                if (e.Value != null && e.Value != DBNull.Value)
+                {
+                    var secondsLeft = (TimeSpan)e.Value;
+                    if (secondsLeft.TotalSeconds < 0)
+                    {
+                        e.DisplayText = "0";
+                    }
+                    else
+                    {
+                        if (secondsLeft.TotalHours >= 24)
+                            e.DisplayText = secondsLeft.ToString(@"d\.hh\:mm\m");
+                        else if (secondsLeft.TotalMinutes >= 60)
+                            e.DisplayText = secondsLeft.ToString(@"hh\:mm\:ss");
+                        else if (secondsLeft.TotalSeconds >= 60)
+                            e.DisplayText = secondsLeft.ToString(@"mm\:ss");
+                        else
+                            e.DisplayText = secondsLeft.ToString("ss");
+                    }
+                }
+            }
+
+            if (e.Column.FieldName == "Sold Time")
+            {
+                if (e.Value != DBNull.Value)
+                {
+                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
+                    if (d?.SoldTime != null)
+                        e.DisplayText = d.SoldTime.ToString();
+                }
+            }
+
+            //posted time
+            if (e.Column.FieldName == "Seller Registration")
+            {
+                e.Column.SortMode = ColumnSortMode.Value;
+                if (view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob") is DataList d)
+                    if (d.SellerRegistration != null)
+                        e.DisplayText = d.SellerRegistration.ToString("yyyy-MM-dd");
+            }
+
+            if (Debugger.IsAttached && e.Column.FieldName.Contains("Relist"))
+            {
+                if (e.Value != DBNull.Value)
+                {
+                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
+                    e.DisplayText = d.LastStatusCheck.LocalTime.ToString("hh:mm:ss");
+                }
+            }
+
+            if (e.Column.FieldName == "Returns")
+            {
+                if (e.Value != DBNull.Value)
+                {
+                    var d = (DataList)view.GetListSourceRowCellValue(e.ListSourceRowIndex, "Blob");
+                    if (d?.HighBidder != null && Form1._showHighBidder)
+                    {
+                        e.DisplayText = d.HighBidder;
+                        e.Column.SortMode = ColumnSortMode.DisplayText;
+                    }
+                    else
+                    {
+                        if (e.Value != null)
+                            e.DisplayText = e.Value.ToString();
+                    }
+                }
+            }
+        }
+
+        internal static void gridView1_SelectionChanged(object sender, SelectionChangedEventArgs e)
+        {
+            TopRowFocus.LastFocusedTime = DateTimeOffset.UtcNow;
+        }
+
+        private static void OnDeleteRowClick(object sender, EventArgs e)
+        {
+            var grView = (GridView)((DXMenuItem)sender).Tag;
+            grView.DeleteSelectedRows();
+        }
+
+        private static void OnCheckoutClick(object sender, EventArgs e)
+        {
+            var row = (DataRow)((DXMenuItem)sender).Tag;
+            var d = (DataList)row["Blob"];
+            Browser.LaunchBrowser(d.EbayAccount?.BrowserPath, d.GetCheckoutLink());
+        }
+
+        private static void OnMakeOfferClick(object sender, EventArgs e)
+        {
+            var row = (DataRow)((DXMenuItem)sender).Tag;
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.MakeOffer);
+        }
+
+        private static void OnBuyItNowClick(object sender, EventArgs e)
+        {
+            var row = (DataRow)((DXMenuItem)sender).Tag;
+            Placeoffer.ShowPlaceOffer(row, Placeoffer.OrderAction.CommitToBuy);
+        }
+
+        public static void gridView1_RowCellClick(object sender, RowCellClickEventArgs e)
+        {
+            ProgramState.Idlesw.Restart();
+            var grView = (AdvBandedGridView)sender;
+            var dataRow = grView.GetDataRow(e.RowHandle);
+            if (dataRow?.RowState is not DataRowState.Detached and not DataRowState.Deleted)
+                if (e.Column.FieldName == "ItemID")
+                {
+                    var d = (DataList)dataRow["Blob"];
+                    if (!string.IsNullOrEmpty(d.GetAffiliateLink()))
+                        Browser.OpenAffiliateLink(d);
+
+                    //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
+                }
+
+            if (e.Column.FieldName.Contains("Price") && !string.IsNullOrEmpty(e.CellValue?.ToString()))
+            {
+                //if (!Config.IsAllowed(ProgramState.SerialNumber))
+                //AutoMeasurement.Client.TrackEvent(e.Column.FieldName, "Grid one click", Analytics.GAid);
+                Placeoffer.ShowPlaceOffer(dataRow, Placeoffer.OrderAction.CommitToBuy,
+                    !UserSettings.ClickOnPriceOpensProductPage);
+            }
+
+            if (e.Column.FieldName == "Seller Name")
+            {
+                var hitInfo = grView.CalcHitInfo(e.Location);
+                // Check if the click is on the correct column
+                if (grView.GetViewInfo() is not GridViewInfo)
+                {
+                    return;
+                }
+
+                // Assuming the icon is drawn on the right side of the cell
+                var cellValueRect = ((GridViewInfo)grView.GetViewInfo())
+                    .GetGridCellInfo(hitInfo.RowHandle, hitInfo.Column).Bounds;
+                var icon = Resources.Blocked; // Your icon resource
+                var iconWidth = icon.Width;
+                var iconHeight = icon.Height;
+
+                // Define the icon's rectangle area within the cell
+                var iconRect = new Rectangle(cellValueRect.Right - (int)iconWidth - 10, // 10 pixels from the right edge
+                    cellValueRect.Y + (cellValueRect.Height - (int)iconHeight) / 2, (int)iconWidth, (int)iconHeight);
+
+                // Check if the click was within the icon's bounds
+                if (iconRect.Contains(e.Location))
+                {
+                    var sellerName = e.CellValue?.ToString();
+                    if (!string.IsNullOrEmpty(sellerName))
+                    {
+                        // Call the method to remove the seller, now that you have the correct name
+                        RemoveSellerFromGridViews(sellerName);
+                    }
+                }
+            }
+
+            if (e.Column.FieldName is "Feedback Score" or "Feedback Rating")
+            {
+                if (grView.GetViewInfo() is not GridViewInfo)
+                {
+                    return;
+                }
+
+                var feedbackCall = new GetFeedbackCall(ConnectionConfig.GetGuestApiContext(SiteCodeType.US));
+                feedbackCall.DetailLevelList = new DetailLevelCodeTypeCollection { DetailLevelCodeType.ReturnAll };
+                feedbackCall.ApiRequest.EndUserIP = "201.201.201." + new Random().Next(1, 200);
+                var sellerName = dataRow["Seller Name"].ToString();
+                feedbackCall.UserID = sellerName;
+                feedbackCall.Execute();
+                var feedbackTable = GetFeedbackSummaryAsText(feedbackCall.FeedbackSummary);
+                var feedbacks = "";
+                foreach (FeedbackDetailType feedback in feedbackCall.FeedbackList)
+                {
+                    var feedbackStyle = "";
+                    var timePassed = DateTime.Now.Subtract(feedback.CommentTime);
+
+                    var timeTxt = "";
+                    if (timePassed.Days > 365)
+                    {
+                        timeTxt = (timePassed.Days / 365).ToString() + "y";
+                    }
+                    else if (timePassed.Days > 30)
+                    {
+                        timeTxt = Math.Round((double)timePassed.Days / 30).ToString() + "mo";
+                    }
+                    else if (timePassed.Days > 7)
+                    {
+                        timeTxt = (timePassed.Days / 7).ToString() + "w";
+                    }
+                    else
+                    {
+                        timeTxt = timePassed.Days.ToString() + "d";
+                    }
+
+                    switch (feedback.CommentType)
+                    {
+                        case CommentTypeCodeType.Positive:
+                            feedbacks += $"[{timeTxt}] {feedback.ItemTitle ?? "+"}\r\n";
+                            break;
+                        case CommentTypeCodeType.Neutral:
+                            feedbacks += $"<backcolor='orange'>[{timeTxt}] {feedback.ItemTitle}</backcolor>\r\n";
+                            break;
+                        case CommentTypeCodeType.Negative:
+                            feedbacks += $"<backcolor=255,102,102>[{timeTxt}] [{feedback.CommentText}] {"\t" + feedback.ItemTitle}</backcolor>\r\n";
+                            break;
+                    }
+                }
+
+                var summaryAsText = $"<b>\r\n{feedbackTable}</b><br>\r\n{feedbacks}";
+
+                var toolTipItem = new ToolTipItem();
+                toolTipItem.Text = summaryAsText;
+                var superToolTip = new SuperToolTip();
+                superToolTip.Items.Add(toolTipItem);
+                superToolTip.AllowHtmlText = DefaultBoolean.True;
+                superToolTip.MaxWidth = 1000;
+                var args = new ToolTipControllerShowEventArgs
+                {
+                    SuperTip = superToolTip,
+                    AllowHtmlText = DefaultBoolean.True,
+                    ToolTipType = ToolTipType.SuperTip,
+                    ToolTipLocation = ToolTipLocation.BottomRight
+                };
+
+                ToolTipController.DefaultController.KeepWhileHovered = true;
+                ToolTipController.DefaultController.AutoPopDelay = 30 * 1000;
+                ToolTipController.DefaultController.AllowHtmlText = true;
+                ToolTipController.DefaultController.Appearance.Font = new Font("Courier New", 10);
+                ToolTipController.DefaultController.ShowHint(args);
+            }
+
+            if (e.Column.FieldName == "Term")
+            {
+                var termText = e.CellValue?.ToString();
+                if (!string.IsNullOrEmpty(termText))
+                {
+                    // Regex pattern to match URLs in the text
+                    var urlPattern = @"(http|https):\/\/[^\s/$.?#].[^\s]*";
+                    var regex = new Regex(urlPattern, RegexOptions.IgnoreCase);
+
+                    // Check if the text contains a URL
+                    var match = regex.Match(termText);
+                    if (match.Success)
+                    {
+                        try
+                        {
+                            // Open the first matched URL in the browser
+                            Process.Start(match.Value);
+                        }
+                        catch (Exception ex)
+                        {
+                            // Handle any exceptions, e.g., log the error or show a message to the user
+                            XtraMessageBox.Show("Failed to open URL: " + ex.Message);
+                        }
+                    }
+                }
+            }
+        }
+
+        public static string GetFeedbackSummaryAsText(FeedbackSummaryType feedbackSummary)
+        {
+            // Set a fixed column width
+            int columnWidth = 7;
+
+            // Table Headers
+            string header = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}", "[1M]", "[6M]", "[12M]");
+
+            // Row for Positive Feedback
+            string positiveRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
+                feedbackSummary.PositiveFeedbackPeriodArray[0].Count,
+                feedbackSummary.PositiveFeedbackPeriodArray[1].Count,
+                feedbackSummary.PositiveFeedbackPeriodArray[2].Count);
+
+            // Row for Neutral Feedback
+            string neutralRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
+                feedbackSummary.NeutralFeedbackPeriodArray[0].Count,
+                feedbackSummary.NeutralFeedbackPeriodArray[1].Count,
+                feedbackSummary.NeutralFeedbackPeriodArray[2].Count);
+
+            // Row for Negative Feedback
+            string negativeRow = string.Format("{0," + columnWidth + "}{1," + columnWidth + "}{2," + columnWidth + "}",
+                feedbackSummary.NegativeFeedbackPeriodArray[0].Count,
+                feedbackSummary.NegativeFeedbackPeriodArray[1].Count,
+                feedbackSummary.NegativeFeedbackPeriodArray[2].Count);
+
+            // Combine all rows with line breaks
+            return string.Join(Environment.NewLine, header, $"<color=grey>{positiveRow}</color>", $"<color=orange>{neutralRow}</color>", $"<color=255,102,102>{negativeRow}</color>");
+        }
+
+
+
+
+        private static string PadRight(string text, int length)
+        {
+            return text.PadRight(length);
+        }
+
+        public static string GetFeedbackSummaryAsText2(FeedbackSummaryType feedbackSummary)
+        {
+            StringBuilder summaryText = new StringBuilder();
+
+
+
+            // Negative Feedback Period Array
+            foreach (var feedbackPeriod in feedbackSummary.NegativeFeedbackPeriodArray.ToArray())
+            {
+                summaryText.AppendLine($"Negative: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
+            }
+
+            // Neutral Feedback Period Array
+            foreach (var feedbackPeriod in feedbackSummary.NeutralFeedbackPeriodArray.ToArray())
+            {
+                summaryText.AppendLine($"Neutral Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
+            }
+
+            // Positive Feedback Period Array
+            foreach (var feedbackPeriod in feedbackSummary.PositiveFeedbackPeriodArray.ToArray())
+            {
+                summaryText.AppendLine($"Positive Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
+            }
+
+            // Seller Rating Summary Array
+            foreach (var ratingSummary in feedbackSummary.SellerRatingSummaryArray.ToArray())
+            {
+                foreach (var ratingDetails in ratingSummary.AverageRatingDetails.ToArray())
+                {
+                    summaryText.AppendLine($"Seller Rating - {ratingDetails.RatingDetail}: {ratingDetails.Rating} ({ratingDetails.RatingCount} ratings) in {ratingSummary.FeedbackSummaryPeriod} period.");
+                }
+            }
+
+            // Seller Role Metrics
+            if (feedbackSummary.SellerRoleMetrics != null)
+            {
+                summaryText.AppendLine(
+                    $"Seller Role Metrics - Positive Feedback Left: {feedbackSummary.SellerRoleMetrics.PositiveFeedbackLeftCount}, " +
+                    $"Neutral Feedback Left: {feedbackSummary.SellerRoleMetrics.NeutralFeedbackLeftCount}, " +
+                    $"Negative Feedback Left: {feedbackSummary.SellerRoleMetrics.NegativeFeedbackLeftCount}, " +
+                    $"Feedback Left Percent: {feedbackSummary.SellerRoleMetrics.FeedbackLeftPercent}%, " +
+                    $"Cross Border Transaction Count: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionCount}, " +
+                    $"Cross Border Transaction Percent: {feedbackSummary.SellerRoleMetrics.CrossBorderTransactionPercent}%, " +
+                    $"Repeat Buyer Count: {feedbackSummary.SellerRoleMetrics.RepeatBuyerCount}, " +
+                    $"Repeat Buyer Percent: {feedbackSummary.SellerRoleMetrics.RepeatBuyerPercent}%, " +
+                    $"Unique Buyer Count: {feedbackSummary.SellerRoleMetrics.UniqueBuyerCount}, " +
+                    $"Transaction Percent: {feedbackSummary.SellerRoleMetrics.TransactionPercent}%.");
+            }
+
+            // Total Feedback Period Array
+            foreach (var feedbackPeriod in feedbackSummary.TotalFeedbackPeriodArray.ToArray())
+            {
+                summaryText.AppendLine($"Total Feedback: {feedbackPeriod.Count} in {feedbackPeriod.PeriodInDays} days.");
+            }
+
+            // Unique Feedback Counts
+            summaryText.AppendLine($"Unique Positive Feedback: {feedbackSummary.UniquePositiveFeedbackCount}, " +
+                                   $"Unique Neutral Feedback: {feedbackSummary.UniqueNeutralFeedbackCount}, " +
+                                   $"Unique Negative Feedback: {feedbackSummary.UniqueNegativeFeedbackCount}.");
+
+            return summaryText.ToString();
+        }
+        public static void gridView1_PopupMenuShowing(object sender, PopupMenuShowingEventArgs e)
+        {
+            var grView = (GridView)sender;
+            if (e.MenuType == GridMenuType.Row)
+            {
+                var rowHandle = e.HitInfo.RowHandle;
+                var row = grView.GetDataRow(rowHandle);
+
+                if (row == null)
+                    return;
+                var d = (DataList)row["Blob"];
+                var menuItemBuyNow = new DXMenuItem("&Buy Now", OnBuyItNowClick, Form1.Instance.barButtonBuy.Glyph);
+                var menuItemCheckout = new DXMenuItem("&Checkout page", OnCheckoutClick, Form1.Instance.barButtonBuy.Glyph);
+
+                if (d.CommitToBuy && !d.Variation)
+                {
+                    var f = menuItemBuyNow.Appearance.Font;
+                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Regular, f.Unit, f.GdiCharSet);
+                    menuItemBuyNow.Appearance.ForeColor = Color.Black;
+                }
+                else
+                {
+                    var f = menuItemBuyNow.Appearance.Font;
+                    menuItemBuyNow.Appearance.Font = new Font(f.ToString(), f.Size, FontStyle.Underline, f.Unit, f.GdiCharSet);
+                    menuItemBuyNow.Appearance.ForeColor = Color.Blue;
+
+                    if (e != null)
+                    {
+                        if (e.HitInfo != null)
+                        {
+                            if (e.HitInfo.Column != null)
+                            {
+                                if (e.HitInfo.Column.FieldName != null)
+                                {
+                                }
+                            }
+                        }
+                    }
+                }
+
+                var font = menuItemCheckout.Appearance.Font;
+                menuItemCheckout.Appearance.Font = new Font(font.ToString(), font.Size, FontStyle.Underline, font.Unit, font.GdiCharSet);
+                menuItemCheckout.Appearance.ForeColor = Color.Blue;
+
+                var menuItemBestOffer = new DXMenuItem("&Make Offer", OnMakeOfferClick);
+                menuItemBestOffer.SvgImage = Resources.MakeOffer;
+                if (row["Best Offer"].ToString() == "False")
+                {
+                    menuItemBestOffer.Enabled = false;
+                }
+
+                var menuItemDeleteRow = new DXMenuItem("&Delete Rows (Del)", OnDeleteRowClick, Form1.Instance.imageList16.Images[1]);
+                var menuItemIgnoreSeller = new DXMenuItem("&Ignore Seller", OnIgnoreSellerClick);
+
+                menuItemBuyNow.Tag = row;
+                menuItemCheckout.Tag = row;
+                menuItemBestOffer.Tag = row;
+                menuItemDeleteRow.Tag = grView;
+                menuItemIgnoreSeller.Tag = row;
+                e.Menu.Items.Add(menuItemBuyNow);
+                e.Menu.Items.Add(menuItemCheckout);
+                e.Menu.Items.Add(menuItemBestOffer);
+                e.Menu.Items.Add(menuItemDeleteRow);
+                e.Menu.Items.Add(menuItemIgnoreSeller);
+
+                //var menuItemBuyUsingPaypal = new DXMenuItem("&Buy Using Paypal", OnBuyUsingPaypal);
+                //e.Menu.Items.Add(menuItemBuyUsingPaypal);
+            }
+
+            if (e.MenuType == GridMenuType.Column)
+            {
+                var dxMenuItemExportToFile = new DXMenuItem("Export to Excel", OnExportToExcelClick);
+                dxMenuItemExportToFile.SvgImage = Resources.Export;
+                dxMenuItemExportToFile.Tag = grView;
+                e.Menu.Items.Add(dxMenuItemExportToFile);
+
+                var dxMenuItemResetLayout = new DXMenuItem("Reset Grid Layout", OnResetGridLayoutClick);
+                dxMenuItemResetLayout.SvgImage = Resources.ResetLayout;
+                dxMenuItemResetLayout.Tag = grView;
+                e.Menu.Items.Add(dxMenuItemResetLayout);
+                if (e.Menu.Items.Count > 7)
+                {
+                    e.Menu.Items.Insert(7, new DXMenuItem("Update all items status", UpdateAllItemsStatus));
+                    e.Menu.Items.Insert(7, new DXMenuItem("Add custom columns", Form1.ShowCustomColumnsWindow));
+                    var llmMenuItem = new DXMenuItem("Update LLM columns", AiAnalysis.UpdateAnalysis);
+                    llmMenuItem.Tag = grView;
+                    e.Menu.Items.Insert(7, llmMenuItem);
+                }
+            }
+
+            //gridBand1.Visible = true;
+            //gridBand2.Visible = true;
+            //gridBand3.Visible = true;
+        }
+
+        private static void OnIgnoreSellerClick(object sender, EventArgs e)
+        {
+            var row = (DataRow)((DXMenuItem)sender).Tag;
+            if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
+            {
+                return;
+            }
+
+            var d = (DataList)row["Blob"];
+            RemoveSellerFromGridViews(d.SellerName);
+        }
+
+        internal static void RemoveSellerFromGridViews(string sellerName)
+        {
+            //ask user if he is sure
+            var result = XtraMessageBox.Show($"Block '{sellerName}'?", "Block Seller", MessageBoxButtons.YesNo);
+            if (result != DialogResult.Yes)
+                return;
+            UserSettings.BlockedSellers.Add(sellerName);
+            var uniqGrids = GridBuilder.GetUniqGrids(Form1.Instance._ebaySearches.ChildrenCore);
+            foreach (var grView in uniqGrids)
+            {
+                grView.BeginDataUpdate();
+                var dataTable = (DataTable)grView.GridControl.DataSource;
+                var i = 0;
+                while (dataTable.Rows.Count > 0 && i < dataTable.Rows.Count)
+                {
+                    if (dataTable.Rows[i]["Seller Name"].ToString() == sellerName)
+                        dataTable.Rows.RemoveAt(i);
+                    else
+                        i++;
+                }
+
+                grView.EndDataUpdate();
+            }
+        }
+
+        private static async void UpdateAllItemsStatus(object sender, EventArgs e)
+        {
+            /*
+            var updatedItems = GetItemsStatus.FetchBatchStatus(batch);
+            GetItemsStatus.UpdateGrids(batch, updatedItems);
+            new Tuple<SimpleItemType, DataRow>(simpleItem, itemRowPair.Value
+            */
+            var itemDict = new Dictionary<string, DataRow>();
+            foreach (var view in ResultsView.ViewsDict)
+            {
+                var datatable = (DataTable)view.Value.DataSource;
+                var i = 0;
+                while (i < datatable?.Rows.Count)
+                {
+                    var row = datatable.Rows[i];
+                    var dataList = (DataList)row["Blob"];
+
+                    if (dataList.Status != Status.Active && dataList.Status != Status.Unknown && dataList.Status != Status.Updated)
+                    {
+                        i++;
+
+                        continue;
+                    }
+
+                    itemDict.Add(dataList.ItemID, row);
+                    i++;
+                }
+            }
+
+            var batches = itemDict.Batch(20);
+
+            //foreach (IEnumerable<KeyValuePair<string, DataRow>> batch in batches)
+            //{
+            //    var simpleItems = await Task.Run(() => GetItemsStatus.FetchBatchStatus(batch.ToList().Select(pair => pair.Key).ToList()));
+            //    foreach (var simpleItem in simpleItems)
+            //    {
+            //        if (itemDict.TryGetValue(simpleItem.ItemID, out var row))
+            //        {
+            //            ((DataList)row["Blob"]).LastStatusCheck = new Time.DateTimeWithDiff()
+            //            {
+            //                TimeDiff = 0,
+            //                Utc = DateTime.UtcNow
+            //            };
+
+            //            if (!Form1.Instance.IsDisposed)
+            //                GetItemsStatus.ListingStatusChanged(new Tuple<SimpleItemType, DataRow>(simpleItem, row));
+            //        }
+            //    }
+            //}
+            foreach (var iEnumerable in batches)
+            {
+                var batch = iEnumerable.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
+                var updatedItems = await Task.Run(() => GetItemsStatus.FetchBatchStatusBrowseAPI(batch.Select(pair => pair.Key).ToList()));
+                if (updatedItems != null)
+                {
+                    GetItemsStatus.UpdateGrids(batch, updatedItems);
+                }
+            }
+        }
+
+        public static void gridView1_RowDeleted(object sender, RowDeletedEventArgs e)
+        {
+            if (e.RowHandle == 0)
+            {
+                UpdateDataOnRowChange((AdvBandedGridView)sender, e.RowHandle);
+            }
+
+            if (((AdvBandedGridView)sender).RowCount == 0)
+            {
+                GuiChanger.ClearItemInfoOnZeroRows();
+            }
+        }
+
+        public static void gridView1_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
+        {
+            if (e.FocusedRowHandle == GridControl.InvalidRowHandle)
+                return;
+            //LastTopRowHandle = ((AdvBandedGridView)sender).FocusedRowHandle - ((AdvBandedGridView)sender).TopRowIndex;
+            var gridView = ((AdvBandedGridView)sender);
+            var firstVisibleRow = gridView.GetVisibleRowHandle(0);
+            if (rowCount < gridView.RowCount)
+            {
+                if (e.FocusedRowHandle - e.PrevFocusedRowHandle == 1)
+                {
+                    if (gridView.GridControl.Cursor == Cursors.Hand)
+                    {
+                        gridView.TopRowIndex++;
+
+
+                        var dataRow = FocusRouter.FocusedGridView.GetDataRow(0);
+
+                        if (dataRow == null)
+                            return;
+                        var args = gridView.GridControl.ToolTipController.CreateShowArgs();
+                        args.ToolTip = dataRow["Title"].ToString();
+                        args.IconType = ToolTipIconType.None;
+                        args.ImageIndex = -1;
+                        args.IconSize = ToolTipIconSize.Small;
+                        args.ToolTipAnchor = ToolTipAnchor.Object;
+
+                        GridView view = gridView;
+                        var info = view.GetViewInfo() as GridViewInfo;
+                        if (info != null)
+                        {
+                            var colInfo = info.ColumnsInfo;
+
+                            var titleColumn = view.Columns["Title"];
+                            if (titleColumn != null && colInfo != null)
+                            {
+                                if (gridView.GridControl?.Parent?.Parent?.Parent == null)
+                                    return;
+
+                                var isFloatingPanel = gridView.GridControl.Parent.Parent.Parent.Parent == null;
+                                var gridColumnInfoArgs = colInfo[titleColumn];
+                                if (gridColumnInfoArgs == null)
+                                {
+                                    return;
+                                }
+
+                                var rect = gridColumnInfoArgs.Bounds;
+
+                                var xFormLocation = 0;
+                                var xPanelLocation = 0;
+                                var xColumn = rect.X;
+                                var yFormLocation = 0;
+                                var yPanelLocation = 0;
+                                var yGridLocation = 0;
+
+                                if (isFloatingPanel)
+                                {
+                                    if (gridView.GridControl.Parent.Parent is DockPanel dockPanel)
+                                    {
+                                        xPanelLocation = dockPanel.FloatLocation.X;
+                                        yPanelLocation = dockPanel.FloatLocation.Y + 5;
+                                    }
+                                }
+                                else
+                                {
+                                    xFormLocation = Form1.Instance.Location.X;
+                                    xPanelLocation = gridView.GridControl.Parent.Parent.Parent.Location.X;
+                                    yFormLocation = Form1.Instance.Location.Y;
+                                    yPanelLocation = gridView.GridControl.Parent.Parent.Parent.Parent.Location.Y;
+                                    yGridLocation = gridView.GridControl.Location.Y;
+                                }
+
+                                var cursorPosition = new Point(xFormLocation + xPanelLocation + 5 + xColumn, yFormLocation + yPanelLocation + yGridLocation);
+                                gridView.GridControl.ToolTipController.ShowHint(args, cursorPosition);
+                            }
+                        }
+                    }
+                }
+            }
+
+            //rowCount = gridView.RowCount;
+            //gridView.OptionsNavigation.AutoFocusNewRow = true; 
+            gridView.OptionsBehavior.KeepFocusedRowOnUpdate = false;
+            //gridView.OptionsBehavior.ImmediateUpdateRowPosition = true;
+            //Debug.WriteLine("LastTopRowHandle:" + e.FocusedRowHandle + "/" + (e.FocusedRowHandle - e.PrevFocusedRowHandle) + "/" + gridView.GridControl.Cursor);
+            //if (e.FocusedRowHandle - e.PrevFocusedRowHandle == 1)
+            //((AdvBandedGridView) sender).TopRowIndex++;            
+            UpdateDataOnRowChange((AdvBandedGridView)sender, e.FocusedRowHandle);
+        }
+
+        public static bool GridUpDownHold = false;
+        public static bool FocusedRowChanged = false;
+
+        public static void UpdateDataOnRowChange(AdvBandedGridView grView, int focusedRowHandle)
+        {
+            FocusedRowChanged = true;
+
+            if (GridUpDownHold)
+                return;
+
+            if (grView != FocusRouter.FocusedGridView)
+                return;
+
+            var row = GetDataRow(grView, focusedRowHandle);
+
+            if (row == null)
+                return;
+
+            var d = (DataList)row["Blob"];
+            if (ProgramState.LastSelectedItemID != d.ItemID || string.IsNullOrEmpty(ProgramState.LastSelectedItemID))
+            {
+                try
+                {
+                    var ctRowChange = GuiChanger.ResetCancelToken();
+                    ProgramState.LastSelectedItemID = d.ItemID;
+                    //if (!Form1.Instance.layoutControl1.IsUpdateLocked)
+                    Form1.Instance.layoutControl1.BeginUpdate();
+                    try
+                    {
+                        GuiChanger.UpdateItemDetailsPanel(d);
+                        ItemSpecifics.UpdateItemDetailsPanelSpecifics(grView, row, Form1.Instance.layoutControl1);
+                    }
+                    catch (Exception ex)
+                    {
+                        ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
+                    }
+
+                    Form1.Instance.layoutControl1.EndUpdate();
+                    GuiChanger.UpdateButtons(d);
+
+                    GuiChanger.ClearGalleryControl();
+                    ImageCache.Inst.ClearCache();
+
+
+                    var pics = new List<string> { d.GalleryUrl };
+                    pics.AddRange(d.Pictures);
+                    GuiChanger.UpdatePicPanel(pics, ctRowChange);
+                    GuiChanger.UpdateExternalData(UserSettings.ExternalEndpointUrl, d.Row);
+                    if (Form1.Instance.dockDescription.Visibility != DockVisibility.Visible
+                        || d.SellerName.Contains("kathe_laz")
+                        || d.SellerName.Contains("wirelessmobilepc")
+                        || d.SellerName.Contains("oklets1"))
+                        return;
+
+                    GuiChanger.UpdateBrowser(d.ItemID, row, Form1.Instance.CancelTokenOnRowChange);
+                }
+                catch (TaskCanceledException)
+                {
+                }
+                catch (OperationCanceledException)
+                {
+                }
+                catch (FileNotFoundException)
+                {
+                }
+                catch (RowNotInTableException)
+                {
+                }
+                catch (IOException ioex)
+                {
+                    if (Debugger.IsAttached)
+                    {
+                        MessageBox.Show(@"Update Avatar: " + ioex.Message);
+                    }
+                }
+                catch (ArgumentNullException anex)
+                {
+                    if (anex.Message != "Additional information: Value cannot be nul")
+                        ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
+                }
+                catch (Exception ex)
+                {
+                    ExM.ubuyExceptionHandler("CellButtonClicked: ", ex);
+                }
+            }
+        }
+
+        private static DataRow GetDataRow(AdvBandedGridView grView, int focusedRowHandle)
+        {
+            DataRow row = null;
+            try
+            {
+                row = grView.GetDataRow(focusedRowHandle);
+
+                if (row == null || row.RowState == DataRowState.Deleted || row.RowState == DataRowState.Detached)
+                    return null;
+            }
+            catch (ArgumentNullException anex)
+            {
+                if (anex.Message != "Value cannot be null.\r\nParameter name: key")
+                    ExM.ubuyExceptionHandler("CellButtonClicked: ", anex);
+            }
+            catch (Exception)
+            {
+                // ignored
+            }
+
+            return row;
+        }
+
+        public static void GridView1_GotFocus(object sender, EventArgs e)
+        {
+            FocusRouter.FocusedGridView = (GridView)sender;
+            if (FocusRouter.FocusedGridView.RowCount == 1)
+            {
+                if (FocusRouter.FocusedGridView.FocusedRowHandle == GridControl.InvalidRowHandle)
+                    return;
+
+                //UpdateDataOnRowChange((AdvBandedGridView)sender, FocusRouter.FocusedGridView.FocusedRowHandle);
+            }
+        }
+
+        public static void GridView1_Click(object sender, EventArgs e)
+        {
+            if (FocusRouter.FocusedGridView != ((GridView)sender))
+            {
+                FocusRouter.FocusedGridView = ((GridView)sender);
+                FocusRouter.FocusedGridView.InvalidateRow(FocusRouter.FocusedGridView.FocusedRowHandle);
+            }
+        }
+
+        public static void OnResetGridLayoutClick(object sender, EventArgs e)
+        {
+            var result = XtraMessageBox.Show("Are you sure?\nGrid settings like columns size, order, visibility, etc. will be set to a default state", "", MessageBoxButtons.OKCancel);
+            if (result == DialogResult.OK)
+            {
+                var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
+                var datasource = grView.GridControl.DataSource;
+                grView.GridControl.DataSource = null;
+                grView.Columns.Clear();
+                grView.GridControl.DataSource = datasource;
+                GridBuilder.ResetGridViewLayout(grView);
+            }
+        }
+
+        private static string _exportFolderLocation = "";
+
+        private static void OnExportToExcelClick(object sender, EventArgs e)
+        {
+            var dialog = new FolderBrowserDialog();
+            dialog.Description = "Export to folder";
+            if (!string.IsNullOrWhiteSpace(_exportFolderLocation))
+                dialog.SelectedPath = _exportFolderLocation;
+            var result = dialog.ShowDialog();
+            if (result != DialogResult.OK)
+                return;
+
+            _exportFolderLocation = dialog.SelectedPath;
+            var grView = (AdvBandedGridView)((DXMenuItem)sender).Tag;
+            var fileName = $"uBuyFirst Export {DateTime.Now.ToString("u").Replace(":", ".").Replace("Z", "")}.csv";
+            var exportPath = Path.Combine(_exportFolderLocation, fileName);
+            var exportRows = new List<String>();
+            var headers = grView.VisibleColumns.Select(c => c.FieldName.ToString()).ToList();
+            var headerRow = Helpers.CreateCSVRow(headers);
+            exportRows.Add(headerRow);
+            for (var i = 0; i < grView.RowCount; i++)
+            {
+                var exportCells = new List<String>();
+                foreach (var header in headers)
+                {
+                    var cellText = grView.GetRowCellDisplayText(i, header);
+                    exportCells.Add(cellText);
+                }
+
+                var properRow = Helpers.CreateCSVRow(exportCells);
+                exportRows.Add(properRow);
+            }
+
+            var fileContents = string.Join("\r\n", exportRows);
+            File.WriteAllText(exportPath, fileContents);
+            Process.Start(exportPath);
+        }
+
+        private static int rowCount;
+
+        public static void gridView1_RowCountChanged(object sender, EventArgs e)
+        {
+            var grView = (AdvBandedGridView)sender;
+            if (grView.RowCount == 1)
+            {
+                TopRowFocus.FocusTopRow(grView, 0);
+            }
+
+            if (rowCount < grView.RowCount)
+            {
+                //grView.TopRowIndex++;//= grView.RowCount - rowCount;
+            }
+
+            //rowCount = grView.RowCount;
+        }
+
+        public static void gridView1_TopRowChanged(object sender, EventArgs e)
+        {
+            return;
+
+            var gridView = ((AdvBandedGridView)sender);
+            if (rowCount < gridView.RowCount)
+            {
+                gridView.TopRowIndex++; //= grView.RowCount - rowCount;
+            }
+
+            rowCount = gridView.RowCount;
+        }
+
+        public static void GridView1_FormatRuleDataUpdateCustomTrigger(object sender, FormatRuleGridDataUpdateTriggerEventArgs e)
+        {
+            if (double.TryParse(e.NewValue.ToString(), out var price))
+            {
+                if (price > 0)
+                {
+                    e.Trigger = true;
+                    return;
+                }
+            }
+
+            e.Trigger = false;
+        }
+    }
+}
diff --git a/EbaySniper/ILLink/ILLink.Descriptors.LibraryBuild.xml b/EbaySniper/ILLink/ILLink.Descriptors.LibraryBuild.xml
new file mode 100644
index ********..a42d7f0e
--- /dev/null
+++ b/EbaySniper/ILLink/ILLink.Descriptors.LibraryBuild.xml
@@ -0,0 +1,8 @@
+﻿<linker>
+  <assembly fullname="System.Diagnostics.DiagnosticSource">
+    <type fullname="System.Diagnostics.Metrics.MetricsEventSource">
+      <!-- Used by System.Private.CoreLib via reflection to init the EventSource -->
+      <method name="GetInstance" />
+    </type>
+  </assembly>
+</linker>
diff --git a/EbaySniper/Item/ItemSpecifics.cs b/EbaySniper/Item/ItemSpecifics.cs
index 61786ed5..97d64721 100644
--- a/EbaySniper/Item/ItemSpecifics.cs
+++ b/EbaySniper/Item/ItemSpecifics.cs
@@ -4,7 +4,9 @@ using System.Data;
 using System.Drawing;
 using System.Linq;
 using BrowseAPI;
+using DevExpress.Utils;
 using DevExpress.XtraEditors;
+using DevExpress.XtraEditors.Repository;
 using DevExpress.XtraGrid.Views.BandedGrid;
 using DevExpress.XtraLayout;
 using eBay.Service.Core.Soap;
@@ -124,19 +126,26 @@ namespace uBuyFirst.Item
                     continue;
                 dataTable.Columns.Add(columnName);
 
-                AddItemSpecificColumnToGridView(grView, columnName);
+                AddCustomColumnToGridView(grView, columnName);
 
                 AddSpecificToLayoutControl(columnName, Form1.Instance.layoutControl1);
             }
         }
 
-        public static void AddItemSpecificColumnToGridView(AdvBandedGridView grView, string columnName)
+        public static void AddCustomColumnToGridView(AdvBandedGridView grView, string columnName)
         {
             grView.BeginUpdate();
             var column = (BandedGridColumn)grView.Columns.AddVisible(columnName);
             column.Caption = columnName;
             column.AutoFillDown = true;
             column.OptionsColumn.AllowEdit = false;
+            if (column.Name.Contains("Reasoning"))
+            {
+                column.AppearanceCell.Options.UseTextOptions = true;
+                column.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
+                column.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
+                column.ColumnEdit = new RepositoryItemMemoEdit();
+            }
             grView.Bands.LastVisibleBand.Columns.Add(column);
             grView.EndUpdate();
         }
diff --git a/EbaySniper/NewItem.cs b/EbaySniper/NewItem.cs
index ac042829..6744cb22 100644
--- a/EbaySniper/NewItem.cs
+++ b/EbaySniper/NewItem.cs
@@ -282,6 +282,10 @@ namespace uBuyFirst
                         return new Tuple<string, DataRow>("RSS2 issue", null);
                     }
 
+                    if (string.IsNullOrEmpty(datalist.Brand))
+                    {
+
+                    }
                     row = dataTable.NewRow();
                     row["Blob"] = datalist;
                     var itemTitleMatches = KeywordHelpers.IsMatch(datalist.Title, datalist.Keyword2Find.LuceneQuery);
diff --git a/EbaySniper/Nullable/AllowNullAttribute.cs b/EbaySniper/Nullable/AllowNullAttribute.cs
new file mode 100644
index ********..8ce3e017
--- /dev/null
+++ b/EbaySniper/Nullable/AllowNullAttribute.cs
@@ -0,0 +1,73 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that <see langword="null"/> is allowed as an input even if the
+    ///     corresponding type disallows it.
+    /// </summary>
+#endif
+    [AttributeUsage(
+        AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.Property,
+        Inherited = false
+    )]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class AllowNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="AllowNullAttribute"/> class.
+        /// </summary>
+#endif
+        public AllowNullAttribute() { }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/DisallowNullAttribute.cs b/EbaySniper/Nullable/DisallowNullAttribute.cs
new file mode 100644
index ********..dd2f7d6b
--- /dev/null
+++ b/EbaySniper/Nullable/DisallowNullAttribute.cs
@@ -0,0 +1,73 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that <see langword="null"/> is disallowed as an input even if the
+    ///     corresponding type allows it.
+    /// </summary>
+#endif
+    [AttributeUsage(
+        AttributeTargets.Field | AttributeTargets.Parameter | AttributeTargets.Property,
+        Inherited = false
+    )]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class DisallowNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="DisallowNullAttribute"/> class.
+        /// </summary>
+#endif
+        public DisallowNullAttribute() { }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/DoesNotReturnAttribute.cs b/EbaySniper/Nullable/DoesNotReturnAttribute.cs
new file mode 100644
index ********..71cce0d3
--- /dev/null
+++ b/EbaySniper/Nullable/DoesNotReturnAttribute.cs
@@ -0,0 +1,70 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that a method that will never return under any circumstance.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Method, Inherited = false)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class DoesNotReturnAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="DoesNotReturnAttribute"/> class.
+        /// </summary>
+        ///
+#endif
+        public DoesNotReturnAttribute() { }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/DoesNotReturnIfAttribute.cs b/EbaySniper/Nullable/DoesNotReturnIfAttribute.cs
new file mode 100644
index ********..3c54fe96
--- /dev/null
+++ b/EbaySniper/Nullable/DoesNotReturnIfAttribute.cs
@@ -0,0 +1,88 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that the method will not return if the associated <see cref="Boolean"/>
+    ///     parameter is passed the specified value.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Parameter, Inherited = false)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class DoesNotReturnIfAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets the condition parameter value.
+        ///     Code after the method is considered unreachable by diagnostics if the argument
+        ///     to the associated parameter matches this value.
+        /// </summary>
+#endif
+        public bool ParameterValue { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="DoesNotReturnIfAttribute"/>
+        ///     class with the specified parameter value.
+        /// </summary>
+        /// <param name="parameterValue">
+        ///     The condition parameter value.
+        ///     Code after the method is considered unreachable by diagnostics if the argument
+        ///     to the associated parameter matches this value.
+        /// </param>
+#endif
+        public DoesNotReturnIfAttribute(bool parameterValue)
+        {
+            ParameterValue = parameterValue;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/MaybeNullAttribute.cs b/EbaySniper/Nullable/MaybeNullAttribute.cs
new file mode 100644
index ********..dce8e389
--- /dev/null
+++ b/EbaySniper/Nullable/MaybeNullAttribute.cs
@@ -0,0 +1,74 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that an output may be <see langword="null"/> even if the
+    ///     corresponding type disallows it.
+    /// </summary>
+#endif
+    [AttributeUsage(
+        AttributeTargets.Field | AttributeTargets.Parameter |
+        AttributeTargets.Property | AttributeTargets.ReturnValue,
+        Inherited = false
+    )]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class MaybeNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="MaybeNullAttribute"/> class.
+        /// </summary>
+#endif
+        public MaybeNullAttribute() { }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/MaybeNullWhenAttribute.cs b/EbaySniper/Nullable/MaybeNullWhenAttribute.cs
new file mode 100644
index ********..3f29fd65
--- /dev/null
+++ b/EbaySniper/Nullable/MaybeNullWhenAttribute.cs
@@ -0,0 +1,85 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that when a method returns <see cref="ReturnValue"/>, 
+    ///     the parameter may be <see langword="null"/> even if the corresponding type disallows it.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Parameter, Inherited = false)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class MaybeNullWhenAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets the return value condition.
+        ///     If the method returns this value, the associated parameter may be <see langword="null"/>.
+        /// </summary>
+#endif
+        public bool ReturnValue { get; }
+
+#if DEBUG
+        /// <summary>
+        ///      Initializes the attribute with the specified return value condition.
+        /// </summary>
+        /// <param name="returnValue">
+        ///     The return value condition.
+        ///     If the method returns this value, the associated parameter may be <see langword="null"/>.
+        /// </param>
+#endif
+        public MaybeNullWhenAttribute(bool returnValue)
+        {
+            ReturnValue = returnValue;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/MemberNotNullAttribute.cs b/EbaySniper/Nullable/MemberNotNullAttribute.cs
new file mode 100644
index ********..d948d1b5
--- /dev/null
+++ b/EbaySniper/Nullable/MemberNotNullAttribute.cs
@@ -0,0 +1,96 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that the method or property will ensure that the listed field and property members have
+    ///     not-<see langword="null"/> values.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Property, Inherited = false, AllowMultiple = true)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class MemberNotNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets field or property member names.
+        /// </summary>
+#endif
+        public string[] Members { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with a field or property member.
+        /// </summary>
+        /// <param name="member">
+        ///     The field or property member that is promised to be not-null.
+        /// </param>
+#endif
+        public MemberNotNullAttribute(string member)
+        {
+            Members = new[] { member };
+        }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with the list of field and property members.
+        /// </summary>
+        /// <param name="members">
+        ///     The list of field and property members that are promised to be not-null.
+        /// </param>
+#endif
+        public MemberNotNullAttribute(params string[] members)
+        {
+            Members = members;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/MemberNotNullWhenAttribute.cs b/EbaySniper/Nullable/MemberNotNullWhenAttribute.cs
new file mode 100644
index ********..8bbe44d8
--- /dev/null
+++ b/EbaySniper/Nullable/MemberNotNullWhenAttribute.cs
@@ -0,0 +1,114 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that the method or property will ensure that the listed field and property members have
+    ///     non-<see langword="null"/> values when returning with the specified return value condition.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Method | AttributeTargets.Property, Inherited = false, AllowMultiple = true)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class MemberNotNullWhenAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets the return value condition.
+        /// </summary>
+#endif
+        public bool ReturnValue { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Gets field or property member names.
+        /// </summary>
+#endif
+        public string[] Members { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with the specified return value condition and a field or property member.
+        /// </summary>
+        /// <param name="returnValue">
+        ///     The return value condition. If the method returns this value,
+        ///     the associated parameter will not be <see langword="null"/>.
+        /// </param>
+        /// <param name="member">
+        ///     The field or property member that is promised to be not-<see langword="null"/>.
+        /// </param>
+#endif
+        public MemberNotNullWhenAttribute(bool returnValue, string member)
+        {
+            ReturnValue = returnValue;
+            Members = new[] { member };
+        }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with the specified return value condition and list
+        ///     of field and property members.
+        /// </summary>
+        /// <param name="returnValue">
+        ///     The return value condition. If the method returns this value,
+        ///     the associated parameter will not be <see langword="null"/>.
+        /// </param>
+        /// <param name="members">
+        ///     The list of field and property members that are promised to be not-null.
+        /// </param>
+#endif
+        public MemberNotNullWhenAttribute(bool returnValue, params string[] members)
+        {
+            ReturnValue = returnValue;
+            Members = members;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/NotNullAttribute.cs b/EbaySniper/Nullable/NotNullAttribute.cs
new file mode 100644
index ********..ec3724ec
--- /dev/null
+++ b/EbaySniper/Nullable/NotNullAttribute.cs
@@ -0,0 +1,74 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that an output is not <see langword="null"/> even if the
+    ///     corresponding type allows it.
+    /// </summary>
+#endif
+    [AttributeUsage(
+        AttributeTargets.Field | AttributeTargets.Parameter |
+        AttributeTargets.Property | AttributeTargets.ReturnValue,
+        Inherited = false
+    )]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class NotNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Initializes a new instance of the <see cref="NotNullAttribute"/> class.
+        /// </summary>
+#endif
+        public NotNullAttribute() { }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/NotNullIfNotNullAttribute.cs b/EbaySniper/Nullable/NotNullIfNotNullAttribute.cs
new file mode 100644
index ********..72ee057b
--- /dev/null
+++ b/EbaySniper/Nullable/NotNullIfNotNullAttribute.cs
@@ -0,0 +1,91 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that the output will be non-<see langword="null"/> if the
+    ///     named parameter is non-<see langword="null"/>.
+    /// </summary>
+#endif
+    [AttributeUsage(
+        AttributeTargets.Parameter | AttributeTargets.Property | AttributeTargets.ReturnValue,
+        AllowMultiple = true,
+        Inherited = false
+    )]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class NotNullIfNotNullAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets the associated parameter name.
+        ///     The output will be non-<see langword="null"/> if the argument to the
+        ///     parameter specified is non-<see langword="null"/>.
+        /// </summary>
+#endif
+        public string ParameterName { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with the associated parameter name.
+        /// </summary>
+        /// <param name="parameterName">
+        ///     The associated parameter name.
+        ///     The output will be non-<see langword="null"/> if the argument to the
+        ///     parameter specified is non-<see langword="null"/>.
+        /// </param>
+#endif
+        public NotNullIfNotNullAttribute(string parameterName)
+        {
+            ParameterName = parameterName;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Nullable/NotNullWhenAttribute.cs b/EbaySniper/Nullable/NotNullWhenAttribute.cs
new file mode 100644
index ********..9887fabd
--- /dev/null
+++ b/EbaySniper/Nullable/NotNullWhenAttribute.cs
@@ -0,0 +1,85 @@
+// <auto-generated>
+//   This code file has automatically been added by the "Nullable" NuGet package (https://www.nuget.org/packages/Nullable).
+//   Please see https://github.com/manuelroemer/Nullable for more information.
+//
+//   IMPORTANT:
+//   DO NOT DELETE THIS FILE if you are using a "packages.config" file to manage your NuGet references.
+//   Consider migrating to PackageReferences instead:
+//   https://docs.microsoft.com/en-us/nuget/consume-packages/migrate-packages-config-to-package-reference
+//   Migrating brings the following benefits:
+//   * The "Nullable" folder and the nullable "*Attribute.cs" files don't appear in your project.
+//   * The added files are immutable and can therefore not be modified by coincidence.
+//   * Updating/Uninstalling the package will work flawlessly.
+// </auto-generated>
+
+#region License
+// MIT License
+// 
+// Copyright (c) Manuel Römer
+// 
+// Permission is hereby granted, free of charge, to any person obtaining a copy
+// of this software and associated documentation files (the "Software"), to deal
+// in the Software without restriction, including without limitation the rights
+// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
+// copies of the Software, and to permit persons to whom the Software is
+// furnished to do so, subject to the following conditions:
+// 
+// The above copyright notice and this permission notice shall be included in all
+// copies or substantial portions of the Software.
+// 
+// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
+// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
+// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
+// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
+// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
+// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
+// SOFTWARE.
+#endregion
+
+#if !NULLABLE_ATTRIBUTES_DISABLE
+#nullable enable
+#pragma warning disable
+
+namespace System.Diagnostics.CodeAnalysis
+{
+    using global::System;
+
+#if DEBUG
+    /// <summary>
+    ///     Specifies that when a method returns <see cref="ReturnValue"/>,
+    ///     the parameter will not be <see langword="null"/> even if the corresponding type allows it.
+    /// </summary>
+#endif
+    [AttributeUsage(AttributeTargets.Parameter, Inherited = false)]
+#if !NULLABLE_ATTRIBUTES_INCLUDE_IN_CODE_COVERAGE
+    [DebuggerNonUserCode]
+#endif
+    internal sealed class NotNullWhenAttribute : Attribute
+    {
+#if DEBUG
+        /// <summary>
+        ///     Gets the return value condition.
+        ///     If the method returns this value, the associated parameter will not be <see langword="null"/>.
+        /// </summary>
+#endif
+        public bool ReturnValue { get; }
+
+#if DEBUG
+        /// <summary>
+        ///     Initializes the attribute with the specified return value condition.
+        /// </summary>
+        /// <param name="returnValue">
+        ///     The return value condition.
+        ///     If the method returns this value, the associated parameter will not be <see langword="null"/>.
+        /// </param>
+#endif
+        public NotNullWhenAttribute(bool returnValue)
+        {
+            ReturnValue = returnValue;
+        }
+    }
+}
+
+#pragma warning restore
+#nullable restore
+#endif // NULLABLE_ATTRIBUTES_DISABLE
diff --git a/EbaySniper/Properties/Settings.Designer.cs b/EbaySniper/Properties/Settings.Designer.cs
index 60da5c6e..e6a3703e 100644
--- a/EbaySniper/Properties/Settings.Designer.cs
+++ b/EbaySniper/Properties/Settings.Designer.cs
@@ -12,7 +12,7 @@ namespace uBuyFirst.Properties {
     
     
     [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
-    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "*********")]
+    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "*********")]
     internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase {
         
         private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));
diff --git a/EbaySniper/Settings.cs b/EbaySniper/Settings.cs
index d862d6fb..9e935aef 100644
--- a/EbaySniper/Settings.cs
+++ b/EbaySniper/Settings.cs
@@ -19,6 +19,7 @@ using DevExpress.XtraGrid.Views.BandedGrid;
 using DevExpress.XtraVerticalGrid.Internal;
 using Microsoft.Win32;
 using PushbulletSharp;
+using uBuyFirst.AI;
 using uBuyFirst.Data.Filters;
 using uBuyFirst.Filters;
 using uBuyFirst.Grid;
@@ -130,6 +131,8 @@ namespace uBuyFirst
             {
                 var settings = Serializator.DeserializeSettings(configPath);
                 ItemSpecifics.CategorySpecificsList.Clear();
+                if (false)
+                    AiAnalysis.AiColumnsList.Clear();
                 if (settings.CategorySpecifics != null)
                 {
                     foreach (var c in settings.CategorySpecifics)
@@ -144,6 +147,21 @@ namespace uBuyFirst
                         }
                     }
                 }
+
+                if (settings.AiColumnsList != null)
+                {
+                    foreach (var c in settings.AiColumnsList)
+                    {
+                        if (c.ToString() != "")
+                        {
+                            var defaultDataTable = GridBuilder.GetDefaultDataTable();
+                            if (defaultDataTable.Columns.Contains(c.ToString()))
+                                continue;
+
+                            AiAnalysis.AiColumnsList.Add(c);
+                        }
+                    }
+                }
                 //backup need datasourcerefresh?
 
 
@@ -586,6 +604,7 @@ namespace uBuyFirst
                 settings.WordsHighlight3 = UserSettings.Highlightsvalues.Words3;
                 settings.Fontsize = UserSettings.BrowserFontSize;
                 settings.CategorySpecifics = ItemSpecifics.CategorySpecificsList;
+                settings.AiColumnsList = AiAnalysis.AiColumnsList;
                 settings.RowHeight = decimal.TryParse(barEditItemRowHeight.EditValue?.ToString(), out var height) ? height : 22m;
                 settings.BrowserBg = UserSettings.BrowserBg;
                 settings.TList = EBayAccountsList.ToList();
diff --git a/EbaySniper/Tools/SettingsClass.cs b/EbaySniper/Tools/SettingsClass.cs
index 860aab45..b0d01b6a 100644
--- a/EbaySniper/Tools/SettingsClass.cs
+++ b/EbaySniper/Tools/SettingsClass.cs
@@ -3,6 +3,7 @@ using System.ComponentModel;
 using System.Reflection;
 using System.Threading;
 using uBuyFirst.Auth;
+using uBuyFirst.CustomClasses;
 using uBuyFirst.Item;
 using uBuyFirst.Network;
 using uBuyFirst.Prefs;
@@ -41,6 +42,7 @@ namespace uBuyFirst.Tools
         public long UBuyFirstRedirectTimestamp { get; set; }
 
         public BindingList<CategorySpecific> CategorySpecifics { get; set; }
+        public CustomBindingList<string> AiColumnsList { get; set; }
 
         public int Fontsize { get; set; }
 
@@ -130,7 +132,7 @@ namespace uBuyFirst.Tools
         public string? ExternalEndpointUrl { get; set; }
         public string SyncSearchTermsFileHash { get; set; }
         public bool SendDescriptionAndPictures { get; set; }
-
+        
         public static readonly ReaderWriterLockSlim SettingsReadWriteLock = new();
     }
 }
diff --git a/EbaySniper/app.config b/EbaySniper/app.config
index b466d725..88729d2a 100644
--- a/EbaySniper/app.config
+++ b/EbaySniper/app.config
@@ -1,174 +1,154 @@
-﻿<?xml version="1.0" encoding="utf-8"?>
-<configuration>
-  <configSections>
-    <section name="DevExpressXpoProfiler" type="DevExpress.Xpo.Logger.ProfilerConfigSection, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" allowLocation="true" allowDefinition="Everywhere" />
-    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
-      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection" requirePermission="false" />
-    </sectionGroup>
-    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
-  </configSections>
-  <applicationSettings>
-    <DevExpress.LookAndFeel.Design.AppSettings>
-      <setting name="DefaultAppSkin" serializeAs="String">
-        <value>Skin/DevExpress Style</value>
-      </setting>
-      <setting name="DefaultPalette" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="TouchUI" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="TouchScaleFactor" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="DirectX" serializeAs="String">
-        <value>True</value>
-      </setting>
-      <setting name="RegisterUserSkins" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="FontBehavior" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="DefaultAppFont" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="DPIAwarenessMode" serializeAs="String">
-        <value>PerMonitorV2</value>
-      </setting>
-      <setting name="CompactUI" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="RegisterBonusSkins" serializeAs="String">
-        <value></value>
-      </setting>
-      <setting name="CustomPaletteCollection" serializeAs="Xml">
-        <value />
-      </setting>
-    </DevExpress.LookAndFeel.Design.AppSettings>
-  </applicationSettings>
-  <appSettings>
-    <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
-    <add key="ClientSettingsProvider.ServiceUri" value="" />
-  </appSettings>
-  <startup>
-    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
-  </startup>
-  <runtime>
-    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
-      <dependentAssembly>
-        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="MsgPack" publicKeyToken="a2625990d5dc0167" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.1.2.0" newVersion="1.1.2.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.1.1.0" newVersion="1.1.1.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Protobuf" publicKeyToken="a7d26565bac4d604" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-3.21.0.0" newVersion="3.21.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Api.CommonProtos" publicKeyToken="3ec5ea7f18953e47" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-2.6.0.0" newVersion="2.6.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.56.0.0" newVersion="1.56.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="SumoLogic.Logging.Common" publicKeyToken="6b03a58229215ab3" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-    </assemblyBinding>
-  </runtime>
-  <!--  <system.diagnostics>
-    <switches>
-      <add name="XmlSerialization.PregenEventLog" value="1" />
-    </switches>
-  </system.diagnostics>
--->
-  <DevExpressXpoProfiler serverType="DevExpress.Xpo.Logger.Transport.LogServer" serverAssembly="DevExpress.Xpo.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" categories="SQL;Session;DataCache" port="52934" />
-  <system.web>
-    <membership defaultProvider="ClientAuthenticationMembershipProvider">
-      <providers>
-        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
-      </providers>
-    </membership>
-    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
-      <providers>
-        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
-      </providers>
-    </roleManager>
-  </system.web>
-  <entityFramework>
-    <providers>
-      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
-      <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
-    </providers>
-  </entityFramework>
-</configuration>
\ No newline at end of file
+﻿<?xml version="1.0" encoding="utf-8"?>
+<configuration>
+  <configSections>
+    <section name="DevExpressXpoProfiler" type="DevExpress.Xpo.Logger.ProfilerConfigSection, DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" allowLocation="true" allowDefinition="Everywhere" />
+    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
+      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection" requirePermission="false" />
+    </sectionGroup>
+    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
+  </configSections>
+  <applicationSettings>
+    <DevExpress.LookAndFeel.Design.AppSettings>
+      <setting name="DefaultAppSkin" serializeAs="String">
+        <value>Skin/DevExpress Style</value>
+      </setting>
+      <setting name="DefaultPalette" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="TouchUI" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="TouchScaleFactor" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="DirectX" serializeAs="String">
+        <value>True</value>
+      </setting>
+      <setting name="RegisterUserSkins" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="FontBehavior" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="DefaultAppFont" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="DPIAwarenessMode" serializeAs="String">
+        <value>PerMonitorV2</value>
+      </setting>
+      <setting name="CompactUI" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="RegisterBonusSkins" serializeAs="String">
+        <value></value>
+      </setting>
+      <setting name="CustomPaletteCollection" serializeAs="Xml">
+        <value />
+      </setting>
+    </DevExpress.LookAndFeel.Design.AppSettings>
+  </applicationSettings>
+  <appSettings>
+    <add key="EnableWindowsFormsHighDpiAutoResizing" value="true" />
+    <add key="ClientSettingsProvider.ServiceUri" value="" />
+  </appSettings>
+  <startup>
+    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
+  </startup>
+  <runtime>   
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">   
+        <dependentAssembly>   
+            <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+    </assemblyBinding>
+  </runtime>
+  <!--  <system.diagnostics>
+    <switches>
+      <add name="XmlSerialization.PregenEventLog" value="1" />
+    </switches>
+  </system.diagnostics>
+-->
+  <DevExpressXpoProfiler serverType="DevExpress.Xpo.Logger.Transport.LogServer" serverAssembly="DevExpress.Xpo.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" categories="SQL;Session;DataCache" port="52934" />
+  <system.web>
+    <membership defaultProvider="ClientAuthenticationMembershipProvider">
+      <providers>
+        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
+      </providers>
+    </membership>
+    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
+      <providers>
+        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
+      </providers>
+    </roleManager>
+  </system.web>
+  <entityFramework>
+    <providers>
+      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer" />
+      <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
+    </providers>
+  </entityFramework>
+</configuration>
diff --git a/EbaySniper/bin/Debug/app.config b/EbaySniper/bin/Debug/app.config
index 2f502454..88729d2a 100644
--- a/EbaySniper/bin/Debug/app.config
+++ b/EbaySniper/bin/Debug/app.config
@@ -52,98 +52,78 @@
     <add key="ClientSettingsProvider.ServiceUri" value="" />
   </appSettings>
   <startup>
-    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2" />
+    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2" />
   </startup>
-  <runtime>
-    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
-      <dependentAssembly>
-        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="MsgPack" publicKeyToken="a2625990d5dc0167" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.1.2.0" newVersion="1.1.2.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.1.1.0" newVersion="1.1.1.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Protobuf" publicKeyToken="a7d26565bac4d604" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-3.21.0.0" newVersion="3.21.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Api.CommonProtos" publicKeyToken="3ec5ea7f18953e47" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-2.6.0.0" newVersion="2.6.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-1.56.0.0" newVersion="1.56.0.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="SumoLogic.Logging.Common" publicKeyToken="6b03a58229215ab3" culture="neutral" />
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
-      </dependentAssembly>
+  <runtime>   
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">   
+        <dependentAssembly>   
+            <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
+        <dependentAssembly>   
+            <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />   
+            <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />   
+        </dependentAssembly>   
     </assemblyBinding>
   </runtime>
   <!--  <system.diagnostics>
@@ -171,4 +151,4 @@
       <provider invariantName="System.Data.SQLite.EF6" type="System.Data.SQLite.EF6.SQLiteProviderServices, System.Data.SQLite.EF6" />
     </providers>
   </entityFramework>
-</configuration>
\ No newline at end of file
+</configuration>
diff --git a/EbaySniper/bin/Debug/uBuyFirst.exe.config b/EbaySniper/bin/Debug/uBuyFirst.exe.config
index 9ade9933..f11071cf 100644
--- a/EbaySniper/bin/Debug/uBuyFirst.exe.config
+++ b/EbaySniper/bin/Debug/uBuyFirst.exe.config
@@ -56,105 +56,109 @@
     <add key="ClientSettingsProvider.ServiceUri" value=""/>
   </appSettings>
   <startup>
-    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.2"/>
+    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.7.2"/>
   </startup>
   <runtime>
     <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
-      <dependentAssembly>
-        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
-      </dependentAssembly>
       <dependentAssembly>
         <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
         <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
       </dependentAssembly>
-      <dependentAssembly>
-        <assemblyIdentity name="System.Net.Http" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0"/>
-      </dependentAssembly>
       <dependentAssembly>
         <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral"/>
         <bindingRedirect oldVersion="0.0.0.0-3.1113.0.0" newVersion="3.1113.0.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="MsgPack" publicKeyToken="a2625990d5dc0167" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-1.0.0.0" newVersion="1.0.0.0"/>
+        <assemblyIdentity name="NLog" publicKeyToken="5120e14c03d0593c" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-5.0.0.1" newVersion="5.0.0.1"/>
+        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
+        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
+        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+      </dependentAssembly>
+      <dependentAssembly>
+        <assemblyIdentity name="Microsoft.Extensions.Logging" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
         <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+        <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.IO.Compression" publicKeyToken="b77a5c561934e089" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0"/>
+        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-1.1.2.0" newVersion="1.1.2.0"/>
+        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-1.1.1.0" newVersion="1.1.1.0"/>
+        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
         <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-6.0.0.1" newVersion="6.0.0.1"/>
+        <assemblyIdentity name="Microsoft.Extensions.Http" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Google.Protobuf" publicKeyToken="a7d26565bac4d604" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-3.21.0.0" newVersion="3.21.0.0"/>
+        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Google.Api.CommonProtos" publicKeyToken="3ec5ea7f18953e47" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-2.6.0.0" newVersion="2.6.0.0"/>
+        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="Google.Apis.Auth" publicKeyToken="4b01fa6e34db77ab" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-1.56.0.0" newVersion="1.56.0.0"/>
+        <assemblyIdentity name="CefSharp.Core.Runtime" processorArchitecture="x86" publicKeyToken="40c4b6fc221f4138" culture="neutral"/>
+        <codeBase version="**********" href="x86/CefSharp.Core.Runtime.dll"/>
       </dependentAssembly>
       <dependentAssembly>
-        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
+        <assemblyIdentity name="CefSharp.Core.Runtime" processorArchitecture="amd64" publicKeyToken="40c4b6fc221f4138" culture="neutral"/>
+        <codeBase version="**********" href="x64/CefSharp.Core.Runtime.dll"/>
       </dependentAssembly>
+    </assemblyBinding>
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
       <dependentAssembly>
-        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
+    </assemblyBinding>
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
       <dependentAssembly>
-        <assemblyIdentity name="SumoLogic.Logging.Common" publicKeyToken="6b03a58229215ab3" culture="neutral"/>
-        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
+        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
+    </assemblyBinding>
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
       <dependentAssembly>
-        <assemblyIdentity name="CefSharp.Core.Runtime" processorArchitecture="x86" publicKeyToken="40c4b6fc221f4138" culture="neutral"/>
-        <codeBase version="**********" href="x86/CefSharp.Core.Runtime.dll"/>
+        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
+    </assemblyBinding>
+    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
       <dependentAssembly>
-        <assemblyIdentity name="CefSharp.Core.Runtime" processorArchitecture="amd64" publicKeyToken="40c4b6fc221f4138" culture="neutral"/>
-        <codeBase version="**********" href="x64/CefSharp.Core.Runtime.dll"/>
+        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral"/>
+        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
       </dependentAssembly>
     </assemblyBinding>
   </runtime>
diff --git a/EbaySniper/bin/Debug/uBuyFirst.vshost.exe.config b/EbaySniper/bin/Debug/uBuyFirst.vshost.exe.config
index 2e1c836c..6b67cc78 100644
--- a/EbaySniper/bin/Debug/uBuyFirst.vshost.exe.config
+++ b/EbaySniper/bin/Debug/uBuyFirst.vshost.exe.config
@@ -27,7 +27,7 @@
       <dependentAssembly>
         <assemblyIdentity name="eBay.Service" publicKeyToken="1d9d786a5932eaf0" culture="neutral" />
         <bindingRedirect oldVersion="0.0.0.0-3.1027.0.0" newVersion="3.1027.0.0" />
-      </dependentAssembly>
+      </dependentAssembly>      
     </assemblyBinding>
   </runtime>
   <!--  <system.diagnostics>
diff --git a/EbaySniper/packages.config b/EbaySniper/packages.config
index f2f9cdc2..5e7491ac 100644
--- a/EbaySniper/packages.config
+++ b/EbaySniper/packages.config
@@ -1,106 +1,116 @@
-﻿<?xml version="1.0" encoding="utf-8"?>
-<packages>
-  <package id="ably.io" version="1.2.11" targetFramework="net462" />
-  <package id="BouncyCastle" version="1.8.9" targetFramework="net462" />
-  <package id="CefSharp.Common" version="131.3.10" targetFramework="net462" />
-  <package id="CefSharp.WinForms" version="131.3.10" targetFramework="net462" />
-  <package id="chromiumembeddedframework.runtime.win-x64" version="131.3.1" targetFramework="net462" />
-  <package id="chromiumembeddedframework.runtime.win-x86" version="131.3.1" targetFramework="net462" />
-  <package id="DalSoft.RestClient" version="4.4.1" targetFramework="net462" />
-  <package id="J2N" version="2.0.0-beta-0017" targetFramework="net462" />
-  <package id="LicenseSpot.Framework" version="3.9.0" targetFramework="net462" />
-  <package id="Lucene.Net" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Lucene.Net.Analysis.Common" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Lucene.Net.Memory" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Lucene.Net.Queries" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Lucene.Net.QueryParser" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Lucene.Net.Sandbox" version="4.8.0-beta00015" targetFramework="net462" />
-  <package id="Microsoft.Bcl.AsyncInterfaces" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Configuration" version="6.0.1" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Configuration.Abstractions" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Configuration.Binder" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.DependencyInjection" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Http" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Logging" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Logging.Abstractions" version="6.0.1" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Options" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Extensions.Primitives" version="6.0.0" targetFramework="net462" />
-  <package id="Microsoft.Net.Compilers" version="4.2.0" targetFramework="net462" developmentDependency="true" />
-  <package id="Microsoft.NETCore.Platforms" version="6.0.4" targetFramework="net462" />
-  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net462" />
-  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net46" />
-  <package id="MQTTnet" version="4.3.3.952" targetFramework="net462" />
-  <package id="MQTTnet.Extensions.ManagedClient" version="4.3.3.952" targetFramework="net462" />
-  <package id="MQTTnet.Extensions.WebSocket4Net" version="4.3.3.952" targetFramework="net462" />
-  <package id="MsgPack.Cli" version="1.0.1" targetFramework="net46" />
-  <package id="NETStandard.Library" version="2.0.3" targetFramework="net462" />
-  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net462" />
-  <package id="NLog" version="5.0.4" targetFramework="net462" />
-  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="*********" targetFramework="net462" />
-  <package id="SumoLogic.Logging.Common" version="*******" targetFramework="net462" />
-  <package id="SumoLogic.Logging.NLog" version="*******" targetFramework="net462" />
-  <package id="SuperSocket.ClientEngine.Core" version="0.10.0" targetFramework="net462" />
-  <package id="System.AppContext" version="4.3.0" targetFramework="net462" />
-  <package id="System.Buffers" version="4.5.1" targetFramework="net462" />
-  <package id="System.Collections" version="4.3.0" targetFramework="net462" />
-  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net462" />
-  <package id="System.Collections.Specialized" version="4.3.0" targetFramework="net462" />
-  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net462" />
-  <package id="System.Console" version="4.3.1" targetFramework="net462" />
-  <package id="System.Data.SQLite.Core" version="1.0.114.3" targetFramework="net462" />
-  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net462" />
-  <package id="System.Diagnostics.DiagnosticSource" version="6.0.0" targetFramework="net462" />
-  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net462" />
-  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net462" />
-  <package id="System.Globalization" version="4.3.0" targetFramework="net462" />
-  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net462" />
-  <package id="System.IO" version="4.3.0" targetFramework="net462" />
-  <package id="System.IO.Compression" version="4.3.0" targetFramework="net462" />
-  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net462" />
-  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net462" />
-  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net462" />
-  <package id="System.Linq" version="4.3.0" targetFramework="net462" />
-  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net462" />
-  <package id="System.Memory" version="4.5.5" targetFramework="net462" />
-  <package id="System.Net.Http" version="4.3.4" targetFramework="net462" />
-  <package id="System.Net.Http.WinHttpHandler" version="6.0.1" targetFramework="net462" />
-  <package id="System.Net.NameResolution" version="4.3.0" targetFramework="net462" />
-  <package id="System.Net.Primitives" version="4.3.1" targetFramework="net462" />
-  <package id="System.Net.Requests" version="4.3.0" targetFramework="net462" />
-  <package id="System.Net.Security" version="4.3.0" targetFramework="net462" />
-  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net462" />
-  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net462" />
-  <package id="System.ObjectModel" version="4.3.0" targetFramework="net462" />
-  <package id="System.Reflection" version="4.3.0" targetFramework="net462" />
-  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net462" />
-  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net462" />
-  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net462" />
-  <package id="System.Runtime" version="4.3.1" targetFramework="net462" />
-  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net462" />
-  <package id="System.Runtime.Extensions" version="4.3.1" targetFramework="net462" />
-  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net462" />
-  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net462" />
-  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net462" />
-  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net462" />
-  <package id="System.Runtime.WindowsRuntime" version="5.0.0-preview.5.20278.1" targetFramework="net462" />
-  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net46" requireReinstallation="true" />
-  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net46" />
-  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net46" />
-  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net462" />
-  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net462" />
-  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net462" />
-  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="net462" />
-  <package id="System.Threading" version="4.3.0" targetFramework="net462" />
-  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net462" />
-  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net462" />
-  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net462" />
-  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net462" />
-  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
-  <package id="System.Xml.ReaderWriter" version="4.3.1" targetFramework="net462" />
-  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net462" />
-  <package id="Telegram.Bot" version="18.0.0" targetFramework="net462" />
-  <package id="Telegram.Bot.Extensions.Polling" version="1.0.2" targetFramework="net462" />
-  <package id="WebSocket4Net" version="0.15.2" targetFramework="net462" />
+﻿<?xml version="1.0" encoding="utf-8"?>
+<packages>
+  <package id="ably.io" version="1.2.11" targetFramework="net472" />
+  <package id="BouncyCastle" version="1.8.9" targetFramework="net472" />
+  <package id="CefSharp.Common" version="131.3.10" targetFramework="net472" />
+  <package id="CefSharp.WinForms" version="131.3.10" targetFramework="net472" />
+  <package id="chromiumembeddedframework.runtime.win-x64" version="131.3.1" targetFramework="net472" />
+  <package id="chromiumembeddedframework.runtime.win-x86" version="131.3.1" targetFramework="net472" />
+  <package id="DalSoft.RestClient" version="4.4.1" targetFramework="net472" />
+  <package id="Humanizer.Core" version="2.14.1" targetFramework="net472" />
+  <package id="J2N" version="2.0.0-beta-0017" targetFramework="net472" />
+  <package id="Json.More.Net" version="2.1.0" targetFramework="net472" />
+  <package id="JsonPointer.Net" version="5.1.0" targetFramework="net472" />
+  <package id="JsonSchema.Net" version="7.3.1" targetFramework="net472" />
+  <package id="JsonSchema.Net.Generation" version="5.0.0" targetFramework="net472" />
+  <package id="LicenseSpot.Framework" version="3.9.0" targetFramework="net472" />
+  <package id="Lucene.Net" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Lucene.Net.Analysis.Common" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Lucene.Net.Memory" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Lucene.Net.Queries" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Lucene.Net.QueryParser" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Lucene.Net.Sandbox" version="4.8.0-beta00015" targetFramework="net472" />
+  <package id="Microsoft.Bcl.AsyncInterfaces" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="Microsoft.CSharp" version="4.7.0" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Configuration" version="6.0.1" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Configuration.Abstractions" version="6.0.0" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Configuration.Binder" version="6.0.0" targetFramework="net472" />
+  <package id="Microsoft.Extensions.DependencyInjection" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Http" version="6.0.0" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Logging" version="9.0.2" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.2" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Options" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="Microsoft.Extensions.Primitives" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="Microsoft.Net.Compilers" version="4.2.0" targetFramework="net472" developmentDependency="true" />
+  <package id="Microsoft.NETCore.Platforms" version="6.0.4" targetFramework="net472" />
+  <package id="Microsoft.Win32.Primitives" version="4.3.0" targetFramework="net472" />
+  <package id="Microsoft.WindowsAzure.ConfigurationManager" version="3.2.3" targetFramework="net472" />
+  <package id="MQTTnet" version="4.3.7.1207" targetFramework="net472" />
+  <package id="MQTTnet.Extensions.ManagedClient" version="4.3.7.1207" targetFramework="net472" />
+  <package id="MQTTnet.Extensions.WebSocket4Net" version="4.3.7.1207" targetFramework="net472" />
+  <package id="Mscc.GenerativeAI" version="2.2.1" targetFramework="net472" />
+  <package id="MsgPack.Cli" version="1.0.1" targetFramework="net472" />
+  <package id="NETStandard.Library" version="2.0.3" targetFramework="net472" />
+  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
+  <package id="NLog" version="5.4.0" targetFramework="net472" />
+  <package id="Nullable" version="1.3.1" targetFramework="net472" developmentDependency="true" />
+  <package id="Stub.System.Data.SQLite.Core.NetFramework" version="1.0.119.0" targetFramework="net472" />
+  <package id="SumoLogic.Logging.Common" version="1.0.1.8" targetFramework="net472" />
+  <package id="SumoLogic.Logging.NLog" version="1.0.1.8" targetFramework="net472" />
+  <package id="SuperSocket.ClientEngine.Core" version="0.10.0" targetFramework="net472" />
+  <package id="System.AppContext" version="4.3.0" targetFramework="net472" />
+  <package id="System.Buffers" version="4.6.0" targetFramework="net472" />
+  <package id="System.Collections" version="4.3.0" targetFramework="net472" />
+  <package id="System.Collections.Concurrent" version="4.3.0" targetFramework="net472" />
+  <package id="System.Collections.Specialized" version="4.3.0" targetFramework="net472" />
+  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net472" />
+  <package id="System.Console" version="4.3.1" targetFramework="net472" />
+  <package id="System.Data.SQLite.Core" version="1.0.119.0" targetFramework="net472" />
+  <package id="System.Diagnostics.Debug" version="4.3.0" targetFramework="net472" />
+  <package id="System.Diagnostics.DiagnosticSource" version="10.0.0-preview.1.25080.5" targetFramework="net472" />
+  <package id="System.Diagnostics.Tools" version="4.3.0" targetFramework="net472" />
+  <package id="System.Diagnostics.Tracing" version="4.3.0" targetFramework="net472" />
+  <package id="System.Globalization" version="4.3.0" targetFramework="net472" />
+  <package id="System.Globalization.Calendars" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO.Compression" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO.Compression.ZipFile" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO.FileSystem" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO.FileSystem.Primitives" version="4.3.0" targetFramework="net472" />
+  <package id="System.IO.Pipelines" version="9.0.2" targetFramework="net472" />
+  <package id="System.Linq" version="4.3.0" targetFramework="net472" />
+  <package id="System.Linq.Expressions" version="4.3.0" targetFramework="net472" />
+  <package id="System.Memory" version="4.6.0" targetFramework="net472" />
+  <package id="System.Net.Http" version="4.3.4" targetFramework="net472" />
+  <package id="System.Net.Http.WinHttpHandler" version="6.0.1" targetFramework="net472" />
+  <package id="System.Net.NameResolution" version="4.3.0" targetFramework="net472" />
+  <package id="System.Net.Primitives" version="4.3.1" targetFramework="net472" />
+  <package id="System.Net.Requests" version="4.3.0" targetFramework="net472" />
+  <package id="System.Net.Security" version="4.3.0" targetFramework="net472" />
+  <package id="System.Net.Sockets" version="4.3.0" targetFramework="net472" />
+  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net472" />
+  <package id="System.ObjectModel" version="4.3.0" targetFramework="net472" />
+  <package id="System.Reflection" version="4.3.0" targetFramework="net472" />
+  <package id="System.Reflection.Extensions" version="4.3.0" targetFramework="net472" />
+  <package id="System.Reflection.Primitives" version="4.3.0" targetFramework="net472" />
+  <package id="System.Resources.ResourceManager" version="4.3.0" targetFramework="net472" />
+  <package id="System.Runtime" version="4.3.1" targetFramework="net472" />
+  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net472" />
+  <package id="System.Runtime.Extensions" version="4.3.1" targetFramework="net472" />
+  <package id="System.Runtime.Handles" version="4.3.0" targetFramework="net472" />
+  <package id="System.Runtime.InteropServices" version="4.3.0" targetFramework="net472" />
+  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net472" />
+  <package id="System.Runtime.Numerics" version="4.3.0" targetFramework="net472" />
+  <package id="System.Runtime.WindowsRuntime" version="5.0.0-preview.5.20278.1" targetFramework="net472" />
+  <package id="System.Security.Cryptography.Algorithms" version="4.3.1" targetFramework="net472" />
+  <package id="System.Security.Cryptography.Encoding" version="4.3.0" targetFramework="net472" />
+  <package id="System.Security.Cryptography.Primitives" version="4.3.0" targetFramework="net472" />
+  <package id="System.Security.Cryptography.X509Certificates" version="4.3.2" targetFramework="net472" />
+  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net472" />
+  <package id="System.Text.Encoding.Extensions" version="4.3.0" targetFramework="net472" />
+  <package id="System.Text.Encodings.Web" version="9.0.2" targetFramework="net472" />
+  <package id="System.Text.Json" version="9.0.2" targetFramework="net472" />
+  <package id="System.Text.RegularExpressions" version="4.3.1" targetFramework="net472" />
+  <package id="System.Threading" version="4.3.0" targetFramework="net472" />
+  <package id="System.Threading.Channels" version="7.0.0" targetFramework="net472" />
+  <package id="System.Threading.Tasks" version="4.3.0" targetFramework="net472" />
+  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net472" />
+  <package id="System.Threading.Timer" version="4.3.0" targetFramework="net472" />
+  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
+  <package id="System.Xml.ReaderWriter" version="4.3.1" targetFramework="net472" />
+  <package id="System.Xml.XDocument" version="4.3.0" targetFramework="net472" />
+  <package id="Telegram.Bot" version="18.0.0" targetFramework="net472" />
+  <package id="Telegram.Bot.Extensions.Polling" version="1.0.2" targetFramework="net472" />
+  <package id="WebSocket4Net" version="0.15.2" targetFramework="net472" />
 </packages>
\ No newline at end of file
diff --git a/EbaySniper/uBuyFirst.csproj b/EbaySniper/uBuyFirst.csproj
index 345808bc..ac560528 100644
--- a/EbaySniper/uBuyFirst.csproj
+++ b/EbaySniper/uBuyFirst.csproj
@@ -1,8 +1,9 @@
-﻿<?xml version="1.0" encoding="utf-8"?>
+<?xml version="1.0" encoding="utf-8"?>
 <Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
   <Import Project="..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props" Condition="Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props')" />
   <Import Project="..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props')" />
   <Import Project="..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props')" />
+  <Import Project="..\packages\Nullable.1.3.1\build\Nullable.props" Condition="Exists('..\packages\Nullable.1.3.1\build\Nullable.props')" />
   <Import Project="..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" />
   <PropertyGroup>
     <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
@@ -33,7 +34,7 @@
     <AppDesignerFolder>Properties</AppDesignerFolder>
     <RootNamespace>uBuyFirst</RootNamespace>
     <AssemblyName>uBuyFirst</AssemblyName>
-    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
+    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
     <SGenUseProxyTypes>false</SGenUseProxyTypes>
     <SGenPlatformTarget>$(Platform)</SGenPlatformTarget>
     <TargetFrameworkProfile>
@@ -115,7 +116,8 @@
       <Private>False</Private>
     </Reference>
     <Reference Include="BouncyCastle.Crypto, Version=1.8.5.0, Culture=neutral, PublicKeyToken=0e99375e54769942">
-      <HintPath>..\packages\BouncyCastle.1.8.5\lib\BouncyCastle.Crypto.dll</HintPath>
+      <HintPath>..\packages\BouncyCastle.1.8.9\lib\BouncyCastle.Crypto.dll</HintPath>
+      <Private>True</Private>
     </Reference>
     <Reference Include="CefSharp, Version=**********, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
       <HintPath>..\packages\CefSharp.Common.131.3.10\lib\net462\CefSharp.dll</HintPath>
@@ -128,7 +130,6 @@
     </Reference>
     <Reference Include="DalSoft.RestClient, Version=1.0.10.0, Culture=neutral, processorArchitecture=MSIL">
       <HintPath>..\packages\DalSoft.RestClient.4.4.1\lib\netstandard2.0\DalSoft.RestClient.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="DevExpress.BonusSkins.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
       <SpecificVersion>False</SpecificVersion>
@@ -210,6 +211,9 @@
     <Reference Include="ExceptionReporter.WinForms">
       <HintPath>bin\Debug\ExceptionReporter.WinForms.dll</HintPath>
     </Reference>
+    <Reference Include="Humanizer, Version=2.14.0.0, Culture=neutral, PublicKeyToken=979442b78dfc278e, processorArchitecture=MSIL">
+      <HintPath>..\packages\Humanizer.Core.2.14.1\lib\netstandard2.0\Humanizer.dll</HintPath>
+    </Reference>
     <Reference Include="IO.Ably, Version=1.2.11.0, Culture=neutral, PublicKeyToken=70c9e5c3d2c68b16, processorArchitecture=MSIL">
       <HintPath>..\packages\ably.io.1.2.11\lib\net46\IO.Ably.dll</HintPath>
     </Reference>
@@ -218,83 +222,98 @@
     </Reference>
     <Reference Include="J2N, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f39447d697a969af, processorArchitecture=MSIL">
       <HintPath>..\packages\J2N.2.0.0-beta-0017\lib\net45\J2N.dll</HintPath>
-      <Private>True</Private>
+    </Reference>
+    <Reference Include="Json.More, Version=2.0.0.0, Culture=neutral, PublicKeyToken=17ed63f672b0e278, processorArchitecture=MSIL">
+      <HintPath>..\packages\Json.More.Net.2.1.0\lib\netstandard2.0\Json.More.dll</HintPath>
+    </Reference>
+    <Reference Include="JsonPointer.Net, Version=*******, Culture=neutral, PublicKeyToken=17ed63f672b0e278, processorArchitecture=MSIL">
+      <HintPath>..\packages\JsonPointer.Net.5.1.0\lib\netstandard2.0\JsonPointer.Net.dll</HintPath>
+    </Reference>
+    <Reference Include="JsonSchema.Net, Version=7.0.0.0, Culture=neutral, PublicKeyToken=17ed63f672b0e278, processorArchitecture=MSIL">
+      <HintPath>..\packages\JsonSchema.Net.7.3.1\lib\netstandard2.0\JsonSchema.Net.dll</HintPath>
+    </Reference>
+    <Reference Include="JsonSchema.Net.Generation, Version=*******, Culture=neutral, PublicKeyToken=17ed63f672b0e278, processorArchitecture=MSIL">
+      <HintPath>..\packages\JsonSchema.Net.Generation.5.0.0\lib\netstandard2.0\JsonSchema.Net.Generation.dll</HintPath>
     </Reference>
     <Reference Include="LicenseSpot.Framework, Version=3.9.0.0, Culture=neutral, PublicKeyToken=de24b71aae0b435d, processorArchitecture=MSIL">
       <HintPath>..\packages\LicenseSpot.Framework.3.9.0\lib\LicenseSpot.Framework.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.4.8.0-beta00015\lib\net45\Lucene.Net.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net.Analysis.Common, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.Analysis.Common.4.8.0-beta00015\lib\net45\Lucene.Net.Analysis.Common.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net.Memory, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.Memory.4.8.0-beta00015\lib\net45\Lucene.Net.Memory.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net.Queries, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.Queries.4.8.0-beta00015\lib\net45\Lucene.Net.Queries.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net.QueryParser, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.QueryParser.4.8.0-beta00015\lib\net45\Lucene.Net.QueryParser.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="Lucene.Net.Sandbox, Version=*******, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
       <HintPath>..\packages\Lucene.Net.Sandbox.4.8.0-beta00015\lib\net45\Lucene.Net.Sandbox.dll</HintPath>
-      <Private>True</Private>
     </Reference>
-    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.6.0.0\lib\net461\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
+    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.10.0.0-preview.1.25080.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
+      <Private>True</Private>
     </Reference>
     <Reference Include="Microsoft.CSharp" />
     <Reference Include="Microsoft.Extensions.Configuration, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
       <HintPath>..\packages\Microsoft.Extensions.Configuration.6.0.1\lib\net461\Microsoft.Extensions.Configuration.dll</HintPath>
     </Reference>
-    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=1.1.2.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.1.1.2\lib\netstandard1.0\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
-      <Private>True</Private>
+    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
     </Reference>
     <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
       <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.6.0.0\lib\net461\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
     </Reference>
-    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.6.0.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.10.0.0-preview.1.25080.5\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
+      <Private>True</Private>
     </Reference>
-    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.10.0.0-preview.1.25080.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
+      <Private>True</Private>
     </Reference>
     <Reference Include="Microsoft.Extensions.Http, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
       <HintPath>..\packages\Microsoft.Extensions.Http.6.0.0\lib\net461\Microsoft.Extensions.Http.dll</HintPath>
     </Reference>
-    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.Logging.6.0.0\lib\net461\Microsoft.Extensions.Logging.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.2\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
     </Reference>
-    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=6.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.2\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
     </Reference>
-    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.Options.6.0.0\lib\net461\Microsoft.Extensions.Options.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.Options.10.0.0-preview.1.25080.5\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
+      <Private>True</Private>
     </Reference>
-    <Reference Include="Microsoft.Extensions.Primitives, Version=1.1.1.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
-      <HintPath>..\packages\Microsoft.Extensions.Primitives.1.1.1\lib\netstandard1.0\Microsoft.Extensions.Primitives.dll</HintPath>
+    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Extensions.Primitives.10.0.0-preview.1.25080.5\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="Microsoft.Win32.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\Microsoft.Win32.Primitives.4.3.0\lib\net46\Microsoft.Win32.Primitives.dll</HintPath>
+      <Private>True</Private>
       <Private>True</Private>
     </Reference>
     <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
       <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.3\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
     </Reference>
-    <Reference Include="MQTTnet, Version=4.3.3.952, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
-      <HintPath>..\packages\MQTTnet.4.3.3.952\lib\net461\MQTTnet.dll</HintPath>
+    <Reference Include="MQTTnet, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
+      <HintPath>..\packages\MQTTnet.4.3.7.1207\lib\net461\MQTTnet.dll</HintPath>
+    </Reference>
+    <Reference Include="MQTTnet.Extensions.ManagedClient, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
+      <HintPath>..\packages\MQTTnet.Extensions.ManagedClient.4.3.7.1207\lib\net461\MQTTnet.Extensions.ManagedClient.dll</HintPath>
     </Reference>
-    <Reference Include="MQTTnet.Extensions.ManagedClient, Version=4.3.3.952, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
-      <HintPath>..\packages\MQTTnet.Extensions.ManagedClient.4.3.3.952\lib\net461\MQTTnet.Extensions.ManagedClient.dll</HintPath>
+    <Reference Include="MQTTnet.Extensions.WebSocket4Net, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
+      <HintPath>..\packages\MQTTnet.Extensions.WebSocket4Net.4.3.7.1207\lib\net461\MQTTnet.Extensions.WebSocket4Net.dll</HintPath>
     </Reference>
-    <Reference Include="MQTTnet.Extensions.WebSocket4Net, Version=4.3.3.952, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
-      <HintPath>..\packages\MQTTnet.Extensions.WebSocket4Net.4.3.3.952\lib\net461\MQTTnet.Extensions.WebSocket4Net.dll</HintPath>
+    <Reference Include="Mscc.GenerativeAI, Version=2.2.1.0, Culture=neutral, processorArchitecture=MSIL">
+      <HintPath>..\packages\Mscc.GenerativeAI.2.2.1\lib\net472\Mscc.GenerativeAI.dll</HintPath>
     </Reference>
     <Reference Include="MsgPack, Version=1.0.0.0, Culture=neutral, PublicKeyToken=a2625990d5dc0167, processorArchitecture=MSIL">
       <HintPath>..\packages\MsgPack.Cli.1.0.1\lib\net46\MsgPack.dll</HintPath>
@@ -303,7 +322,7 @@
       <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
     </Reference>
     <Reference Include="NLog, Version=*******, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
-      <HintPath>..\packages\NLog.5.0.4\lib\net46\NLog.dll</HintPath>
+      <HintPath>..\packages\NLog.5.4.0\lib\net46\NLog.dll</HintPath>
     </Reference>
     <Reference Include="PresentationCore" />
     <Reference Include="PushbulletSharp, Version=3.1.0.0, Culture=neutral, processorArchitecture=MSIL">
@@ -317,11 +336,11 @@
       <SpecificVersion>False</SpecificVersion>
       <HintPath>..\..\SimpleGoogleAnalytics1\GoogleAnalytics\bin\Debug\netstandard2.0\SimpleGoogleAnalytics.dll</HintPath>
     </Reference>
-    <Reference Include="SumoLogic.Logging.Common, Version=*******, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
-      <HintPath>..\packages\SumoLogic.Logging.Common.*******\lib\net45\SumoLogic.Logging.Common.dll</HintPath>
+    <Reference Include="SumoLogic.Logging.Common, Version=1.0.1.8, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
+      <HintPath>..\packages\SumoLogic.Logging.Common.1.0.1.8\lib\net45\SumoLogic.Logging.Common.dll</HintPath>
     </Reference>
-    <Reference Include="SumoLogic.Logging.NLog, Version=*******, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
-      <HintPath>..\packages\SumoLogic.Logging.NLog.*******\lib\net45\SumoLogic.Logging.NLog.dll</HintPath>
+    <Reference Include="SumoLogic.Logging.NLog, Version=1.0.1.8, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
+      <HintPath>..\packages\SumoLogic.Logging.NLog.1.0.1.8\lib\net45\SumoLogic.Logging.NLog.dll</HintPath>
     </Reference>
     <Reference Include="SuperSocket.ClientEngine, Version=0.10.0.0, Culture=neutral, PublicKeyToken=ee9af13f57f00acc, processorArchitecture=MSIL">
       <HintPath>..\packages\SuperSocket.ClientEngine.Core.0.10.0\lib\net45\SuperSocket.ClientEngine.dll</HintPath>
@@ -329,13 +348,17 @@
     <Reference Include="System">
       <HintPath>..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.dll</HintPath>
     </Reference>
-    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
+    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
     </Reference>
     <Reference Include="System.Collections.Specialized, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Collections.Specialized.4.3.0\lib\net46\System.Collections.Specialized.dll</HintPath>
-      <Private>True</Private>
       <Private>True</Private>
+      <HintPath>..\packages\System.Collections.Specialized.4.3.0\lib\net46\System.Collections.Specialized.dll</HintPath>
     </Reference>
     <Reference Include="System.ComponentModel.Annotations, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
       <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
@@ -343,79 +366,187 @@
     <Reference Include="System.ComponentModel.Composition" />
     <Reference Include="System.ComponentModel.DataAnnotations" />
     <Reference Include="System.Configuration" />
+    <Reference Include="System.Console, Version=4.0.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Console.4.3.1\lib\net46\System.Console.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
     <Reference Include="System.Core" />
     <Reference Include="System.Data">
       <Private>False</Private>
     </Reference>
     <Reference Include="System.Data.DataSetExtensions" />
     <Reference Include="System.Data.Linq" />
-    <Reference Include="System.Data.SQLite, Version=*********, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
-      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\lib\net46\System.Data.SQLite.dll</HintPath>
-      <Private>True</Private>
+    <Reference Include="System.Data.SQLite, Version=1.0.119.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
+      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\lib\net46\System.Data.SQLite.dll</HintPath>
     </Reference>
     <Reference Include="System.Deployment" />
     <Reference Include="System.Design" />
-    <Reference Include="System.Diagnostics.DiagnosticSource, Version=5.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.5.0.1\lib\net46\System.Diagnostics.DiagnosticSource.dll</HintPath>
+    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.10.0.0-preview.1.25080.5\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Diagnostics.Tracing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Diagnostics.Tracing.4.3.0\lib\net462\System.Diagnostics.Tracing.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Globalization.Calendars, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Globalization.Calendars.4.3.0\lib\net46\System.Globalization.Calendars.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
-    <Reference Include="System.IO.Compression">
+    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
       <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
       <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.Compression.FileSystem">
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.IO.Pipelines.9.0.2\lib\net462\System.IO.Pipelines.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
-    <Reference Include="System.IO.Compression.FileSystem" />
     <Reference Include="System.Management" />
-    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
+    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
     </Reference>
     <Reference Include="System.Net" />
-    <Reference Include="System.Net.Http">
+    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
       <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
       <Private>True</Private>
+      <Private>True</Private>
     </Reference>
     <Reference Include="System.Net.Http.WinHttpHandler, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
       <HintPath>..\packages\System.Net.Http.WinHttpHandler.6.0.1\lib\net461\System.Net.Http.WinHttpHandler.dll</HintPath>
     </Reference>
     <Reference Include="System.Net.NameResolution, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Net.NameResolution.4.3.0\lib\net46\System.Net.NameResolution.dll</HintPath>
       <Private>True</Private>
       <Private>True</Private>
+      <HintPath>..\packages\System.Net.NameResolution.4.3.0\lib\net46\System.Net.NameResolution.dll</HintPath>
     </Reference>
     <Reference Include="System.Net.Security, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <Private>True</Private>
+      <Private>True</Private>
       <HintPath>..\packages\System.Net.Security.4.3.0\lib\net46\System.Net.Security.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
       <Private>True</Private>
       <Private>True</Private>
     </Reference>
     <Reference Include="System.Numerics" />
-    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
+    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
-    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
+    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Runtime.Extensions, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Runtime.Extensions.4.3.1\lib\net462\System.Runtime.Extensions.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
     <Reference Include="System.Runtime.Remoting" />
     <Reference Include="System.Runtime.Serialization" />
     <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
     <Reference Include="System.Security" />
-    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net46\System.Security.Cryptography.Algorithms.dll</HintPath>
+    <Reference Include="System.Security.Cryptography.Algorithms, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
     <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
       <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
     <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
       <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
+    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
     </Reference>
     <Reference Include="System.ServiceModel" />
     <Reference Include="System.Drawing">
       <Private>False</Private>
     </Reference>
+    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Text.Encodings.Web.9.0.2\lib\net462\System.Text.Encodings.Web.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Text.Json.9.0.2\lib\net462\System.Text.Json.dll</HintPath>
+    </Reference>
+    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
     <Reference Include="System.Threading.Channels, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
       <HintPath>..\packages\System.Threading.Channels.7.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
     </Reference>
-    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
-      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
+    <Reference Include="System.Threading.Tasks" />
+    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.0\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
     </Reference>
     <Reference Include="System.Transactions" />
+    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
+    </Reference>
     <Reference Include="System.Web.Extensions" />
     <Reference Include="System.Web.Services" />
     <Reference Include="System.Windows.Forms" />
@@ -424,12 +555,16 @@
       <Private>False</Private>
     </Reference>
     <Reference Include="System.Xml.Linq" />
+    <Reference Include="System.Xml.ReaderWriter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
+      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.1\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
+      <Private>True</Private>
+      <Private>True</Private>
+    </Reference>
     <Reference Include="Telegram.Bot, Version=18.0.0.0, Culture=neutral, processorArchitecture=MSIL">
       <HintPath>..\packages\Telegram.Bot.18.0.0\lib\netstandard2.0\Telegram.Bot.dll</HintPath>
     </Reference>
     <Reference Include="Telegram.Bot.Extensions.Polling, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL">
       <HintPath>..\packages\Telegram.Bot.Extensions.Polling.1.0.2\lib\netstandard2.0\Telegram.Bot.Extensions.Polling.dll</HintPath>
-      <Private>True</Private>
     </Reference>
     <Reference Include="UIAutomationClient" />
     <Reference Include="WebSocket4Net, Version=0.15.2.11, Culture=neutral, PublicKeyToken=eb4e154b696bf72a, processorArchitecture=MSIL">
@@ -438,6 +573,8 @@
     <Reference Include="WindowsBase" />
   </ItemGroup>
   <ItemGroup>
+    <Compile Include="AI\AiAnalysis.cs" />
+    <Compile Include="AI\GeminiService.cs" />
     <Compile Include="AlertOptionsClass.cs" />
     <Compile Include="API\ShoppingAPI\PricingServiceShoppingAPI.cs" />
     <Compile Include="API\ShoppingAPI\ShoppingAPIJson.cs" />
@@ -537,6 +674,17 @@
     <Compile Include="Network\NetTools.Http2CustomHandler.cs" />
     <Compile Include="Network\UrlFetcher.cs" />
     <Compile Include="Network\WebSocketClient.cs" />
+    <Compile Include="Nullable\AllowNullAttribute.cs" />
+    <Compile Include="Nullable\DisallowNullAttribute.cs" />
+    <Compile Include="Nullable\DoesNotReturnAttribute.cs" />
+    <Compile Include="Nullable\DoesNotReturnIfAttribute.cs" />
+    <Compile Include="Nullable\MaybeNullAttribute.cs" />
+    <Compile Include="Nullable\MaybeNullWhenAttribute.cs" />
+    <Compile Include="Nullable\MemberNotNullAttribute.cs" />
+    <Compile Include="Nullable\MemberNotNullWhenAttribute.cs" />
+    <Compile Include="Nullable\NotNullAttribute.cs" />
+    <Compile Include="Nullable\NotNullIfNotNullAttribute.cs" />
+    <Compile Include="Nullable\NotNullWhenAttribute.cs" />
     <Compile Include="Parsing\DateParser.cs" />
     <Compile Include="Pricing\CurrencyConverter.cs" />
     <Compile Include="Pricing\ItemShipping.cs" />
@@ -863,10 +1011,8 @@
     <None Include="app.manifest">
       <SubType>Designer</SubType>
     </None>
-    <None Include="packages.config">
-      <SubType>Designer</SubType>
-    </None>
     <Compile Include="Prefs\ConnectionConfig.cs" />
+    <None Include="packages.config" />
     <None Include="Properties\DataSources\FilterClass.datasource" />
     <None Include="Properties\DataSources\Form1.Items.datasource" />
     <None Include="Properties\DataSources\Form1.Keyword2Find.datasource" />
@@ -947,6 +1093,7 @@
     <None Include="Resources\Checkout.svg" />
     <None Include="Resources\SortAscending.svg" />
     <None Include="Resources\telegram.svg" />
+    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
     <Content Include="j2n-icon-100x100.png" />
     <Content Include="j2n-icon-100x100.png" />
     <Content Include="LICENSE.txt" />
@@ -980,20 +1127,20 @@
     <PropertyGroup>
       <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
     </PropertyGroup>
-    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
-    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
     <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props'))" />
-    <Error Condition="!Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\build\Microsoft.Extensions.Logging.Abstractions.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\build\Microsoft.Extensions.Logging.Abstractions.targets'))" />
+    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
+    <Error Condition="!Exists('..\packages\Nullable.1.3.1\build\Nullable.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Nullable.1.3.1\build\Nullable.props'))" />
+    <Error Condition="!Exists('..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets'))" />
     <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props'))" />
     <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props'))" />
     <Error Condition="!Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props'))" />
     <Error Condition="!Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets'))" />
+    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
   </Target>
-  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
   <Import Project="..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
-  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.*********\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
-  <Import Project="..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.1\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
+  <Import Project="..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets" Condition="Exists('..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets')" />
   <Import Project="..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets" Condition="Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets')" />
+  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
   <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
        Other similar extension points exist, see Microsoft.Common.targets.
   <Target Name="BeforeBuild">
diff --git a/packages/repositories.config b/packages/repositories.config
deleted file mode 100644
index c52bb8bd..********
--- a/packages/repositories.config
+++ /dev/null
@@ -1,4 +0,0 @@
-﻿<?xml version="1.0" encoding="utf-8"?>
-<repositories>
-  <repository path="..\EbaySniper\packages.config" />
-</repositories>
\ No newline at end of file
