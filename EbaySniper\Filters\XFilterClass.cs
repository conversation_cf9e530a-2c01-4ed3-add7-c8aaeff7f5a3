﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Windows.Forms;
using System.Xml.Serialization;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Exceptions;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.Utils.Serializing;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    [Obfuscation(Exclude = false,
        Feature = "preset(maximum);"
                  + "+anti debug;"
                  + "+anti dump;"
                  + "+anti ildasm;"
                  + "+anti tamper(key=dynamic);"
                  + "+constants;"
                  + "+ctrl flow;"
                  + "+invalid metadata;"
                  + "+ref proxy;"
                  + "-rename;")]
    [Serializable]
    [XmlRoot("XFilters")]
    public class XFilterClass : IComparable
    {
        [NonSerialized]
        private GridFormatRule _gridFormatRule;

        [NonSerialized]
        private ExpressionEvaluator _evaluator;

        private string _alias;
        private bool _enabled;
        private string _expression;
        private string _action;
        private string _formatColumn;
        private CriteriaOperator _filterCriteria;
        private string[] _serializedAppearance;

        // New action system properties
        private string _actionIdentifier;
        private Dictionary<string, object> _actionData;

        [NonSerialized]
        private IFilterAction _actionHandler;

        public XFilterClass()
        {
            SerializedAppearance = new[] { "", "", "" };
            _actionData = new Dictionary<string, object>();
        }

        public bool Enabled
        {
            get => _enabled;
            set
            {
                _enabled = value;
                GridFormatRule.Enabled = _enabled;
            }
        }

        public string Alias
        {
            get => _alias ?? "";
            set => _alias = value;
        }

        public string Action
        {
            get => _action;
            set
            {
                _action = value;
                // Auto-migrate legacy actions
                if (_actionHandler == null && !string.IsNullOrEmpty(value))
                {
                    _actionHandler = FilterActionFactory.CreateFromLegacyAction(value);
                    if (_actionHandler != null)
                    {
                        _actionIdentifier = _actionHandler.ActionTypeIdentifier;
                        // Load any action-specific data
                        _actionHandler.DeserializeActionData(_actionData);
                    }
                }
            }
        }

        public string KeywordScope { get; set; }

        public string FormatColumn
        {
            get => _formatColumn;
            set => _formatColumn = value;
        }

        //Rule for row removal
        public CriteriaOperator FilterCriteria
        {
            get => _filterCriteria;
            set => _filterCriteria = value;
        }

        //Rule for row/cell formatting
        public string Expression
        {
            get => _expression ?? "";
            set => _expression = value;
        }

        public GridFormatRule GridFormatRule
        {
            get => _gridFormatRule;
            set => _gridFormatRule = value;
        }

        public string[] SerializedAppearance
        {
            get => _serializedAppearance;
            set => _serializedAppearance = value;
        }

        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary>
        /// New property for modern action identification
        /// </summary>
        public string ActionIdentifier
        {
            get => _actionIdentifier;
            set
            {
                _actionIdentifier = value;
                if (!string.IsNullOrEmpty(value))
                {
                    _actionHandler = FilterActionFactory.CreateAction(value);
                    if (_actionHandler != null)
                    {
                        _action = _actionHandler.DisplayName; // Keep legacy field updated
                        _actionHandler.DeserializeActionData(_actionData);
                    }
                }
            }
        }

        /// <summary>
        /// Store action-specific data
        /// </summary>
        [XmlIgnore]
        public Dictionary<string, object> ActionData
        {
            get => _actionData ??= new Dictionary<string, object>();
            set => _actionData = value;
        }

        /// <summary>
        /// The modern action handler (not serialized)
        /// </summary>
        [XmlIgnore]
        public IFilterAction ActionHandler
        {
            get => _actionHandler;
            set
            {
                _actionHandler = value;
                if (value != null)
                {
                    _actionIdentifier = value.ActionTypeIdentifier;
                    _action = value.DisplayName; // Keep legacy field updated
                }
            }
        }

        public ExpressionEvaluator GetEvaluator()
        {
            return _evaluator;
        }

        public void Rebuild()
        {
            try
            {
                var descriptors = new PropertyDescriptor[GridBuilder.DefaultDataTable.Columns.Count];
                for (var i = 0; i < descriptors.Length; i++)
                {
                    descriptors[i] = new MyDescriptor(GridBuilder.DefaultDataTable.Columns[i].ColumnName, GridBuilder.DefaultDataTable.Columns[i].DataType, typeof(DataRow));
                }

                var descriptorCollection = new PropertyDescriptorCollection(descriptors);
                _evaluator = new ExpressionEvaluator(descriptorCollection, FilterCriteria, false);
            }
            catch (InvalidPropertyPathException ex)
            {
                Enabled = false;
                XtraMessageBox.Show($"Filter with the name '{Alias}' has an error:\r\n{ex.Message}\r\nPlease edit or remove this filter");
            }
        }

        //Test if rule is valid, all columns exist
        private bool IsCriteriaValid(IEnumerable dataSource, string filterString)
        {
            var op = CriteriaOperator.Parse(filterString, null);
            if (ReferenceEquals(op, null))
                return false;

            var pdc = ListBindingHelper.GetListItemProperties(dataSource);
            var dict = CriteriaColumnAffinityResolver.SplitByColumns(op);
            return dict.Keys.All(item => pdc.Find(item.PropertyName, false) != null);
        }

        public void SerializeAppearance()
        {
            try
            {
                var xfilterAppearance = ((FormatConditionRuleExpression)GridFormatRule.Rule).Appearance;
                SerializedAppearance[0] = XtraSerializeObject(xfilterAppearance, "xfilterAppearance");
                SerializedAppearance[1] = XtraSerializeObject(xfilterAppearance.Options, "xfilterAppearanceOptions");
                SerializedAppearance[2] = XtraSerializeObject(xfilterAppearance.TextOptions, "xfilterAppearanceTextOptions");
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("SerializeAppearance: ", ex);
            }
        }

        internal GridFormatRule DeSerializeGridFormatRule(GridColumn grViewColumn)
        {
            var gridFormatRule = new GridFormatRule
            {
                Rule = new FormatConditionRuleExpression()
            };

            if (grViewColumn != null)
                gridFormatRule.Column = grViewColumn;

            // Check both legacy and new action systems
            if (Action == "Format rows" || ActionHandler is FormatRowsAction)
            {
                gridFormatRule.ApplyToRow = true;
            }

            ((FormatConditionRuleExpression)gridFormatRule.Rule).Expression = _expression;
            gridFormatRule.Tag = Id;

            try
            {
                var xfilterAppearance = ((FormatConditionRuleExpression)gridFormatRule.Rule).Appearance;
                if (SerializedAppearance != null)
                {
                    XtraDeSerializeObject(SerializedAppearance[0].Trim(), xfilterAppearance, "xfilterAppearance");
                    if (SerializedAppearance.Length > 1)
                        XtraDeSerializeObject(SerializedAppearance[1].Trim(), xfilterAppearance.Options, "xfilterAppearanceOptions");
                    if (SerializedAppearance.Length > 2)
                        XtraDeSerializeObject(SerializedAppearance[2].Trim(), xfilterAppearance.TextOptions, "xfilterAppearanceTextOptions");
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("DeSerializeGridFormatRule: ", ex);
            }

            gridFormatRule.Enabled = Enabled;
            return gridFormatRule;
        }

        public static string XtraSerializeObject(object pObject, string pAppName)
        {
            var serializer = new XmlXtraSerializer();
            string result;
            using var stream = new MemoryStream();
            try
            {
                serializer.SerializeObject(pObject, stream, pAppName);

                using var reader = new StreamReader(stream);
                try
                {
                    stream.Position = 0;
                    result = reader.ReadToEnd();
                }
                finally
                {
                    reader.Close();
                }

            }
            finally
            {
                stream.Close();
            }

            return result;
        }

        public static void XtraDeSerializeObject(string pXml, object pObject, string pAppName)
        {
            var serializer = new XmlXtraSerializer();
            if (string.IsNullOrEmpty(pXml))
                return;

            var byteArray = Encoding.ASCII.GetBytes(pXml);

            using var stream = new MemoryStream(byteArray);
            try
            {
                serializer.DeserializeObject(pObject, stream, pAppName);
            }
            catch (Exception)
            {
                // ignored
            }
            finally
            {
                stream.Close();
            }

        }

        public override string ToString()
        {
            return Alias;
        }

        public int CompareTo(object obj)
        {
            return string.Compare(Alias, ((XFilterClass)obj).Alias, StringComparison.OrdinalIgnoreCase);
        }

        public string Export()
        {
            var cells = new List<string>
            {
                Alias,
                Enabled.ToString(),
                Action,
                FormatColumn,
                //cells.Add(Expression);
                FilterCriteria?.ToString(),
                string.Join(";\n", SerializedAppearance).Replace("\"", "\'")
            };

            var row = Helpers.CreateCSVRow(cells);
            return row;
        }

        public static bool IsExpressionValid(string filterString)
        {
            var criteriaOperator = CriteriaOperator.Parse(filterString, null);
            if (criteriaOperator is null)
                return false;

            return true;
        }

        public string Import(List<string> cells)
        {
            var importLog = "";
            try
            {
                var expr = new FormatConditionRuleExpression();
                GridFormatRule = new GridFormatRule
                {
                    Rule = expr
                };

                if (!string.IsNullOrEmpty(cells[0]))
                    Alias = cells[0];
                else
                    importLog += "Alias, ";

                if (bool.TryParse(cells[1], out var enabled))
                    Enabled = enabled;
                else
                    importLog += "Filter Enabled, ";

                if (!string.IsNullOrEmpty(cells[2]) && (new[] { "Format cells", "Format rows", "Remove rows" }.Contains(cells[2]) || cells[2].Contains("Buy with ")))
                    Action = cells[2];
                else
                    importLog += "Action, ";

                var columns = ((AdvBandedGridView)ResultsView.ViewsDict["Results"].MainView)?.Columns;
                if (!string.IsNullOrEmpty(cells[3]) && columns?.ColumnByFieldName(cells[3]) != null)
                    FormatColumn = cells[3];
                else
                    importLog += "FormatColumn, ";

                if (!string.IsNullOrEmpty(cells[4]) && IsExpressionValid(cells[4]))
                {
                    FilterCriteria = CriteriaOperator.Parse(cells[4], null);
                    try
                    {
                        var descriptors = new PropertyDescriptor[GridBuilder.DefaultDataTable.Columns.Count];
                        for (var i = 0; i < descriptors.Length; i++)
                        {
                            descriptors[i] = new MyDescriptor(GridBuilder.DefaultDataTable.Columns[i].ColumnName, GridBuilder.DefaultDataTable.Columns[i].DataType, typeof(DataRow));
                        }

                        var descriptorCollection = new PropertyDescriptorCollection(descriptors);
                        var evaluator = new ExpressionEvaluator(descriptorCollection, FilterCriteria, false);
                        evaluator.Fit(GridBuilder.DefaultDataTable.NewRow());

                        var expression = FilterCriteria.ToString();
                        if (!string.IsNullOrEmpty(expression))
                        {
                            Expression = expression;
                        }
                    }
                    catch (InvalidPropertyPathException ex)
                    {
                        Enabled = false;
                        XtraMessageBox.Show($"Filter '{Alias}'\r\n{ex.Message}\r\nPlease edit or remove this filter");
                    }
                    catch (InvalidOperationException ex)
                    {
                        Enabled = false;
                        XtraMessageBox.Show($"Filter '{Alias}'\r\n{ex.Message}\r\nPlease edit or remove this filter");
                    }
                }
                else
                    importLog += "Filter Expression, ";

                if (!string.IsNullOrEmpty(cells[5]))
                {
                    SerializedAppearance = cells[5].Split(';');
                }
                else
                    importLog += "Style options, ";

                var gridViewColumn = ((AdvBandedGridView)ResultsView.ViewsDict["Results"].MainView)?.Columns[FormatColumn];
                GridFormatRule = DeSerializeGridFormatRule(gridViewColumn);
                Rebuild();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Import filters, filter '" + Alias + "': ", ex);
            }

            return importLog;
        }
    }
}
