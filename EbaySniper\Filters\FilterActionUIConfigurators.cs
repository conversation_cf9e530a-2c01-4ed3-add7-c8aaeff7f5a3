using System;
using System.Collections.Generic;
using System.Linq;
using uBuyFirst.Filters;

namespace uBuyFirst.Filters
{
    /// <summary>
    /// UI configurator for Format Cells action
    /// </summary>
    public class FormatCellsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatCellsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = true,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            if (!string.IsNullOrEmpty(filter.FormatColumn))
            {
                formAccessor.SetSelectedColumn(filter.FormatColumn);
            }
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            filter.FormatColumn = formAccessor.GetSelectedColumn();
        }
    }

    /// <summary>
    /// UI configurator for Format Rows action
    /// </summary>
    public class FormatRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => FormatRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = true
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row formatting
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row formatting
        }
    }

    /// <summary>
    /// UI configurator for Remove Rows action
    /// </summary>
    public class RemoveRowsUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => RemoveRowsAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to load for row removal
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific data to save for row removal
        }
    }

    /// <summary>
    /// UI configurator for Send to Telegram action
    /// </summary>
    public class SendToTelegramUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToTelegramAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to load for Telegram action
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // No specific UI data to save for Telegram action
        }
    }

    /// <summary>
    /// UI configurator for Buy with Account action
    /// </summary>
    public class BuyWithAccountUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => BuyWithAccountAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Account data is handled by the action itself
            // UI configurator only handles UI-specific data
        }
    }

    /// <summary>
    /// UI configurator for Send to Webhook action
    /// </summary>
    public class SendToWebhookUIConfigurator : IFilterActionUIConfigurator
    {
        public string ActionTypeIdentifier => SendToWebhookAction.IDENTIFIER;

        public FilterUIConfiguration GetUIConfiguration()
        {
            return new FilterUIConfiguration
            {
                ShowColumnSelection = false,
                ShowFormatControls = false
                // TODO: In a real implementation, you might add:
                // AdditionalControlsToShow = new List<string> { "webhookUrlTextBox" }
            };
        }

        public void LoadDataFromFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
            // TODO: If you had a webhook URL text box in the UI, you would load it here
        }

        public void SaveDataToFilter(XFilterClass filter, IFormDataAccessor formAccessor)
        {
            // Webhook URL is handled by the action itself
            // UI configurator only handles UI-specific data
            // TODO: If you had a webhook URL text box in the UI, you would save it here
        }
    }
}
