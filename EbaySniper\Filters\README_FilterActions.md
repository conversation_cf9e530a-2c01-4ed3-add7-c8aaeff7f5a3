# Filter Actions System - Implementation Guide

## Overview

The filter actions system has been successfully refactored to use the Strategy Pattern, making it highly extensible and maintainable. This document explains how the new system works and how to add new actions.

## Architecture

### Core Components

1. **IFilterAction Interface** - Defines the contract for all filter actions
2. **FilterActionFactory** - Creates and manages action instances with legacy migration support
3. **Concrete Action Classes** - Implement specific filter behaviors
4. **XFilterClass** - Updated with hybrid approach for backward compatibility
5. **FormXfilters** - Updated UI handling using IFilterActionUIContext
6. **XFilterManager** - Updated execution logic

### Key Benefits

- **Extensibility**: Adding new actions requires only creating a new class and registering it
- **Maintainability**: Each action encapsulates its own logic and UI behavior
- **Backward Compatibility**: Existing filters continue to work seamlessly
- **Type Safety**: No more string comparisons for action dispatch
- **Testability**: Each action can be unit tested independently

## Available Actions

### Built-in Actions

1. **FormatCellsAction** (`FORMAT_CELLS`)
   - Formats individual cells based on criteria
   - Requires format column selection

2. **FormatRowsAction** (`FORMAT_ROWS`)
   - Formats entire rows based on criteria
   - No column selection needed

3. **RemoveRowsAction** (`REMOVE_ROWS`)
   - Removes rows that match criteria
   - No additional configuration

4. **SendToTelegramAction** (`SEND_TO_TELEGRAM`)
   - Sends notifications to Telegram
   - No additional configuration (yet)

5. **BuyWithAccountAction** (`BUY_WITH_ACCOUNT`)
   - Associates eBay account with matching items
   - Stores account username in action data

6. **SendToWebhookAction** (`SEND_TO_WEBHOOK`) - Example Extension
   - Demonstrates how to add new actions
   - Stores webhook URL in action data

## Adding New Actions

### Step 1: Create Action Class

```csharp
public class MyCustomAction : IFilterAction
{
    public const string IDENTIFIER = "MY_CUSTOM_ACTION";
    
    public string DisplayName => "My Custom Action";
    public string ActionTypeIdentifier => IDENTIFIER;

    public void ConfigureUI(IFilterActionUIContext uiContext)
    {
        // Show/hide UI elements as needed
        uiContext.HideUIElements(uiContext.Form.boxColumns);
    }

    public void LoadFromFilter(XFilterClass filter, IFilterActionUIContext uiContext)
    {
        // Load action-specific data from filter.ActionData
    }

    public void SaveToFilter(XFilterClass filter, IFilterActionUIContext uiContext)
    {
        // Save action-specific data to filter.ActionData
    }

    public FilterActionResult Execute(IFilterActionContext context)
    {
        try
        {
            // Implement your action logic here
            return FilterActionResult.CreateSuccess("Action completed");
        }
        catch (Exception ex)
        {
            return FilterActionResult.CreateFailure($"Action failed: {ex.Message}", ex);
        }
    }

    public bool ValidateConfiguration(XFilterClass filter, out string errorMessage)
    {
        errorMessage = null;
        // Add validation logic
        return true;
    }

    public Dictionary<string, object> SerializeActionData()
    {
        // Return action-specific data for serialization
        return new Dictionary<string, object>();
    }

    public void DeserializeActionData(Dictionary<string, object> data)
    {
        // Restore action-specific data from serialization
    }
}
```

### Step 2: Register Action

Add to FilterActionFactory.Initialize():

```csharp
RegisterAction<MyCustomAction>();
```

### Step 3: Test

The new action will automatically appear in the UI dropdown and be available for use.

## Migration and Compatibility

### Automatic Migration

- Legacy filters are automatically migrated when loaded
- String-based actions are converted to action handlers
- No data loss during migration

### Hybrid Support

- Both legacy string actions and new action handlers work simultaneously
- Gradual migration is supported
- Fallback mechanisms ensure continued operation

## Best Practices

### Action Implementation

1. **Use Constants**: Define action identifiers as constants
2. **Validate Input**: Always validate configuration in ValidateConfiguration()
3. **Handle Errors**: Use FilterActionResult for proper error reporting
4. **Store Data**: Use ActionData dictionary for action-specific settings
5. **UI Consistency**: Follow existing patterns for UI configuration

### Performance

1. **Lazy Loading**: Actions are created only when needed
2. **Caching**: Factory caches action instances
3. **Efficient Execution**: Actions execute directly without string comparisons

## Testing

### Unit Testing Actions

```csharp
[Test]
public void MyCustomAction_Execute_ReturnsSuccess()
{
    var action = new MyCustomAction();
    var context = new FilterActionContext
    {
        FilterRule = mockFilter,
        CurrentRow = mockRow
    };
    
    var result = action.Execute(context);
    
    Assert.IsTrue(result.Success);
}
```

## Troubleshooting

### Common Issues

1. **Action Not Appearing**: Ensure it's registered in FilterActionFactory
2. **UI Not Updating**: Check ConfigureUI() implementation
3. **Data Not Persisting**: Verify SerializeActionData/DeserializeActionData
4. **Legacy Filters Broken**: Check legacy migration mappings

### Debugging

- Use FilterActionResult for detailed error information
- Check console output for execution errors
- Verify action registration in factory

## Future Enhancements

### Planned Features

1. **Dynamic UI Generation**: Automatic UI creation based on action properties
2. **Action Dependencies**: Support for actions that depend on other actions
3. **Conditional Actions**: Actions that execute based on conditions
4. **Action Chaining**: Ability to chain multiple actions together

### Extension Points

1. **Custom UI Controls**: Add action-specific UI controls
2. **External Integrations**: Connect to external services
3. **Data Transformations**: Transform data before/after actions
4. **Audit Logging**: Track action execution for compliance

## Conclusion

The new filter actions system provides a robust, extensible foundation for filter functionality. It maintains backward compatibility while enabling easy addition of new features. The Strategy Pattern implementation ensures clean separation of concerns and makes the codebase more maintainable.
